branca-0.8.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
branca-0.8.1.dist-info/LICENSE.txt,sha256=Fv3MFqr_xBAT3mB571tsJl4kAQibA8UHzCanIBFVi6s,1079
branca-0.8.1.dist-info/METADATA,sha256=mH5Y7iuKeIFw-seJvrJ-ataiM3AsuBAKvtE0OBMxx3M,1477
branca-0.8.1.dist-info/RECORD,,
branca-0.8.1.dist-info/WHEEL,sha256=PZUExdf71Ui_so67QXpySuHtCi3-J3wvF4ORK6k_S8U,91
branca-0.8.1.dist-info/top_level.txt,sha256=ypVQTwONob-Ro9YaFnI5JSp3Fie7goG1kHO-OOOvgFE,7
branca/__init__.py,sha256=96-JnqmVvjl6A13m-ngTS54ieHuvW5LsJMR4OEQZ5mo,207
branca/__pycache__/__init__.cpython-312.pyc,,
branca/__pycache__/_version.cpython-312.pyc,,
branca/__pycache__/colormap.cpython-312.pyc,,
branca/__pycache__/element.cpython-312.pyc,,
branca/__pycache__/utilities.cpython-312.pyc,,
branca/_cnames.json,sha256=gczkHOtCOMkbXf3oncGhfdrwrdr850-KBlwdlzuZaB0,3540
branca/_schemes.json,sha256=d19tuEsvPhxMeNf-E8vvQ-1S72JGG5yZvU_nOPZpPsM,21192
branca/_version.py,sha256=9SJ00tY4YILqfP8235Ozvn3QTMNjqfaSLUvcAIBL9pU,21
branca/colormap.py,sha256=fwSd8LsWfz96B3cYq93gf4qjkclpzMkHim57KpIDW44,23040
branca/element.py,sha256=BlCkE9gurVT5utgybkPEYBIWe50Trl2_cfF5pBDk12M,23834
branca/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
branca/scheme_base_codes.json,sha256=yMAJXX7PnL3KFVF3inGtHnuXe3mfegRZ2kkEcQXR3YY,370
branca/scheme_info.json,sha256=4eCiIiCV90kLXtyJbuTCxpDVLPDgEBr2TcHZyKUIe8M,904
branca/templates/color_scale.js,sha256=7gTnXwt4OyGignVmmHCrp_2wf_PCrR2UuhgHkf9s13E,2408
branca/utilities.py,sha256=YORoWiclTR5nN4jFdcBlJsxkoQxhbK_DrazZhYbgpCc,14901
