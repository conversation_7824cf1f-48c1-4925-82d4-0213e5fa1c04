# 📦 Import Required Libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Import custom data utilities
from data import get_leads_data, get_activities_data, Settings

# 📅 Analysis Configuration
ANALYSIS_DATE = '2025-06-26'  # Adjust as needed
print(f"🔍 Analysis configured for: {ANALYSIS_DATE}")

# 🎨 Visualization Settings
plt.style.use('default')
sns.set_palette("husl")
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)

print("✅ Setup complete!")

# 🔍 Load Failed Orders Data
print("🔍 Loading failed orders data from analytics database...")

# Database configuration
wasfaty_leads_config = Settings().wasfaty_leads_db_config
wasfatydb_config = Settings().wasfatydb_config

# Load leads data for failed orders
leads_df = get_leads_data(
    wasfaty_leads_config,
    start_date=ANALYSIS_DATE,
    end_date=ANALYSIS_DATE
)

# Add calculated columns for missing activities
leads_df['missing_activities'] = leads_df['activity_count'] - leads_df['available_activity_count']

# Clean up unnecessary columns
columns_to_drop = ['is_accepted', 'is_allocated', 'is_approved', 'is_partially_approved', 'is_claimed']
leads_df = leads_df.drop(columns=[col for col in columns_to_drop if col in leads_df.columns])

print(f"📊 Loaded {len(leads_df):,} failed orders")
print(f"🏪 Across {leads_df['branch'].nunique()} branches")
print(f"💊 Covering {leads_df['activity_count'].sum():,} total activities")

# Display sample data
print("\n📋 Sample of failed orders data:")
display(leads_df[[
    'order_number', 'branch', 'activity_count', 'available_activity_count', 
    'missing_activities', 'availability_status', 'delivery_type'
]].head())

# 🔍 Load Detailed Activities Data
print("🔍 Loading detailed activities data for failed orders...")

# Get unique order numbers from failed orders
failed_order_numbers = leads_df['order_number'].unique().tolist()
print(f"📋 Fetching activities for {len(failed_order_numbers):,} failed orders")

# Load activities data
activities_df = get_activities_data(
    wasfatydb_config,
    lead_ids=failed_order_numbers,
    start_date=ANALYSIS_DATE
)

# Add delivery type based on tags
activities_df['delivery_type'] = activities_df['tags'].apply(
    lambda x: 'express' if 'express_delivery' in str(x) 
    else 'scheduled' if 'scheduled_delivery' in str(x) 
    else 'unknown'
)

print(f"📊 Loaded {len(activities_df):,} activity records")
print(f"💊 Covering {activities_df['Activities - OrderId__genericCode'].nunique():,} unique generic codes")
print(f"🏪 Across {activities_df['branchLicense'].nunique()} branches")

# Display sample activities data
print("\n📋 Sample of activities data:")
display(activities_df[[
    'orderNumber', 'branchLicense', 'Activities - OrderId__genericCode',
    'Trade Drugs - InternalId__drugCode', 'Trade Drugs - InternalId__isAvailable',
    'Drugs - DrugCode__name', 'delivery_type'
]].head())

# 🔄 Data Integration and Processing
print("🔄 Integrating orders and activities data...")

# Join leads and activities data
joined_df = pd.merge(
    leads_df,
    activities_df,
    left_on='order_number',
    right_on='orderNumber',
    how='left',
    suffixes=('_lead', '_activity')
)

# Add date processing
joined_df['created_at'] = pd.to_datetime(joined_df['createdAt'])
joined_df['date'] = joined_df['created_at'].dt.date
joined_df['hour'] = joined_df['created_at'].dt.hour

# Remove duplicates (same prescription reference and drug code on same date)
print(f"📊 Before deduplication: {len(joined_df):,} records")
joined_df = joined_df.drop_duplicates(
    subset=['erxReference', 'Trade Drugs - InternalId__drugCode', 'date'], 
    keep='first'
)
print(f"📊 After deduplication: {len(joined_df):,} records")

# Create key analysis columns
joined_df['is_mapped'] = joined_df['Is Mapped?'] == 'Yes'
joined_df['is_available'] = joined_df['Trade Drugs - InternalId__isAvailable']
joined_df['generic_code'] = joined_df['Activities - OrderId__genericCode']
joined_df['drug_name'] = joined_df['Drugs - DrugCode__name']
joined_df['branch_license'] = joined_df['branchLicense']

print(f"✅ Data integration complete!")
print(f"📈 Final dataset: {len(joined_df):,} records")
print(f"💊 Unique generic codes: {joined_df['generic_code'].nunique():,}")
print(f"🏪 Unique branches: {joined_df['branch_license'].nunique()}")

# Save processed data for future use
output_file = f'processed_order_loss_data_{ANALYSIS_DATE.replace("-", "")}.csv'
joined_df.to_csv(output_file, index=False)
print(f"💾 Processed data saved to: {output_file}")

# 📊 Order Loss Summary Analysis
print("📊 DAILY ORDER LOSS ANALYSIS SUMMARY")
print("=" * 50)

# Calculate key metrics
total_failed_orders = len(leads_df)
total_generic_codes = joined_df['generic_code'].nunique()
total_activities = len(joined_df)
unavailable_activities = len(joined_df[joined_df['is_available'] == False])
completely_unavailable_orders = len(leads_df[leads_df['availability_status'] == 'unavailable'])
partially_available_orders = len(leads_df[leads_df['availability_status'] == 'partially-available'])

print(f"📅 Analysis Date: {ANALYSIS_DATE}")
print(f"📋 Total Failed Orders: {total_failed_orders:,}")
print(f"💊 Unique Generic Codes: {total_generic_codes:,}")
print(f"🔬 Total Activities: {total_activities:,}")
print(f"❌ Unavailable Activities: {unavailable_activities:,} ({unavailable_activities/total_activities*100:.1f}%)")
print(f"🚫 Completely Unavailable Orders: {completely_unavailable_orders:,} ({completely_unavailable_orders/total_failed_orders*100:.1f}%)")
print(f"⚠️  Partially Available Orders: {partially_available_orders:,} ({partially_available_orders/total_failed_orders*100:.1f}%)")

# Calculate financial impact (assuming average order value)
avg_order_value = 150  # SAR - adjust based on business data
estimated_lost_revenue = total_failed_orders * avg_order_value
print(f"💰 Estimated Lost Revenue: {estimated_lost_revenue:,} SAR")

# 🎯 Top Problematic Generic Codes Analysis
print("\n🎯 TOP PROBLEMATIC MEDICATIONS")
print("=" * 40)

# Calculate generic code failure rates
generic_analysis = joined_df.groupby('generic_code').agg({
    'order_number': 'nunique',  # Number of orders affected
    'is_available': ['count', 'sum'],  # Total activities and available activities
    'drug_name': 'first'  # Get drug name
}).round(2)

# Flatten column names
generic_analysis.columns = ['orders_affected', 'total_activities', 'available_activities', 'drug_name']
generic_analysis['unavailable_activities'] = generic_analysis['total_activities'] - generic_analysis['available_activities']
generic_analysis['availability_rate'] = (generic_analysis['available_activities'] / generic_analysis['total_activities'] * 100).round(1)
generic_analysis['failure_rate'] = (100 - generic_analysis['availability_rate']).round(1)

# Sort by impact (orders affected * failure rate)
generic_analysis['impact_score'] = generic_analysis['orders_affected'] * generic_analysis['failure_rate']
top_problematic = generic_analysis.sort_values('impact_score', ascending=False).head(20)

print("Top 20 Most Problematic Generic Codes:")
print(top_problematic[['drug_name', 'orders_affected', 'failure_rate', 'impact_score']].to_string())

# Save for business use
top_problematic.to_csv(f'top_problematic_medications_{ANALYSIS_DATE.replace("-", "")}.csv')
print(f"\n💾 Detailed analysis saved to: top_problematic_medications_{ANALYSIS_DATE.replace('-', '')}.csv")

# 🏪 Branch Performance Analysis
print("\n🏪 BRANCH PERFORMANCE ANALYSIS")
print("=" * 35)

# Calculate branch-level metrics
branch_analysis = leads_df.groupby('branch').agg({
    'order_number': 'count',  # Total failed orders
    'activity_count': 'sum',  # Total activities
    'available_activity_count': 'sum',  # Available activities
    'missing_activities': 'sum'  # Missing activities
}).round(2)

branch_analysis['availability_rate'] = (branch_analysis['available_activity_count'] / branch_analysis['activity_count'] * 100).round(1)
branch_analysis['orders_per_branch'] = branch_analysis['order_number']
branch_analysis = branch_analysis.sort_values('orders_per_branch', ascending=False)

print("Top 15 Branches by Failed Orders:")
print(branch_analysis[['orders_per_branch', 'availability_rate', 'missing_activities']].head(15).to_string())

# Identify branches with consistently low availability
low_performing_branches = branch_analysis[branch_analysis['availability_rate'] < 70].sort_values('availability_rate')
print(f"\n⚠️  Branches with <70% availability rate: {len(low_performing_branches)}")
if len(low_performing_branches) > 0:
    print(low_performing_branches[['orders_per_branch', 'availability_rate']].head(10).to_string())

# ⏰ Time Pattern Analysis
print("\n⏰ TIME PATTERN ANALYSIS")
print("=" * 25)

# Hourly distribution of failed orders
hourly_failures = leads_df.groupby('hour_of_day').agg({
    'order_number': 'count',
    'available_activity_count': 'mean',
    'activity_count': 'mean'
}).round(2)

hourly_failures['avg_availability_rate'] = (hourly_failures['available_activity_count'] / hourly_failures['activity_count'] * 100).round(1)

print("Failed Orders by Hour of Day:")
print(hourly_failures[['order_number', 'avg_availability_rate']].to_string())

# Peak failure hours
peak_hours = hourly_failures.sort_values('order_number', ascending=False).head(5)
print(f"\n📈 Peak failure hours: {', '.join([f'{h}:00' for h in peak_hours.index.tolist()])}")

# Delivery type analysis
delivery_analysis = leads_df.groupby('delivery_type').agg({
    'order_number': 'count',
    'available_activity_count': 'mean',
    'activity_count': 'mean'
}).round(2)

delivery_analysis['avg_availability_rate'] = (delivery_analysis['available_activity_count'] / delivery_analysis['activity_count'] * 100).round(1)

print("\nFailed Orders by Delivery Type:")
print(delivery_analysis.to_string())

# 🎯 Priority Purchasing Recommendations
print("🎯 PRIORITY PURCHASING RECOMMENDATIONS")
print("=" * 45)

# Create comprehensive purchasing priority list
purchasing_analysis = generic_analysis.copy()
purchasing_analysis['priority_score'] = (
    purchasing_analysis['orders_affected'] * 0.4 +  # Order impact weight
    purchasing_analysis['failure_rate'] * 0.3 +     # Failure rate weight
    purchasing_analysis['unavailable_activities'] * 0.3  # Activity count weight
).round(2)

# Categorize by urgency
def get_urgency_level(row):
    if row['failure_rate'] >= 80 and row['orders_affected'] >= 5:
        return 'CRITICAL'
    elif row['failure_rate'] >= 60 and row['orders_affected'] >= 3:
        return 'HIGH'
    elif row['failure_rate'] >= 40 or row['orders_affected'] >= 2:
        return 'MEDIUM'
    else:
        return 'LOW'

purchasing_analysis['urgency'] = purchasing_analysis.apply(get_urgency_level, axis=1)
purchasing_priorities = purchasing_analysis.sort_values('priority_score', ascending=False)

# Display by urgency level
for urgency in ['CRITICAL', 'HIGH', 'MEDIUM']:
    urgent_items = purchasing_priorities[purchasing_priorities['urgency'] == urgency].head(10)
    if len(urgent_items) > 0:
        print(f"\n🚨 {urgency} PRIORITY ITEMS ({len(urgent_items)} items):")
        print(urgent_items[['drug_name', 'orders_affected', 'failure_rate', 'priority_score']].to_string())

# Save purchasing recommendations
purchasing_priorities.to_csv(f'purchasing_recommendations_{ANALYSIS_DATE.replace("-", "")}.csv')
print(f"\n💾 Purchasing recommendations saved to: purchasing_recommendations_{ANALYSIS_DATE.replace('-', '')}.csv")

# 📈 Daily Inventory KPIs
print("\n📈 DAILY INVENTORY KPIs")
print("=" * 25)

# Calculate key performance indicators
total_unique_medications = joined_df['generic_code'].nunique()
medications_with_issues = len(purchasing_analysis[purchasing_analysis['failure_rate'] > 0])
critical_medications = len(purchasing_analysis[purchasing_analysis['urgency'] == 'CRITICAL'])
high_priority_medications = len(purchasing_analysis[purchasing_analysis['urgency'] == 'HIGH'])

overall_availability_rate = (joined_df['is_available'].sum() / len(joined_df) * 100).round(1)
avg_activities_per_medication = (joined_df.groupby('generic_code')['is_available'].count().mean()).round(1)

print(f"📊 Total Unique Medications Analyzed: {total_unique_medications:,}")
print(f"⚠️  Medications with Availability Issues: {medications_with_issues:,} ({medications_with_issues/total_unique_medications*100:.1f}%)")
print(f"🚨 Critical Priority Medications: {critical_medications:,}")
print(f"🔴 High Priority Medications: {high_priority_medications:,}")
print(f"📈 Overall Availability Rate: {overall_availability_rate}%")
print(f"🔢 Average Activities per Medication: {avg_activities_per_medication}")

# Branch-specific inventory insights
print(f"\n🏪 Branch Performance Summary:")
best_performing_branch = branch_analysis.sort_values('availability_rate', ascending=False).index[0]
worst_performing_branch = branch_analysis.sort_values('availability_rate', ascending=True).index[0]
print(f"🏆 Best Performing Branch: {best_performing_branch} ({branch_analysis.loc[best_performing_branch, 'availability_rate']}% availability)")
print(f"⚠️  Needs Attention: {worst_performing_branch} ({branch_analysis.loc[worst_performing_branch, 'availability_rate']}% availability)")

# 📊 Create Comprehensive Visualizations
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle(f'Wasfaty Order Loss Analysis Dashboard - {ANALYSIS_DATE}', fontsize=16, fontweight='bold')

# 1. Top 10 Problematic Medications
top_10_problematic = top_problematic.head(10)
axes[0, 0].barh(range(len(top_10_problematic)), top_10_problematic['impact_score'], color='red', alpha=0.7)
axes[0, 0].set_yticks(range(len(top_10_problematic)))
axes[0, 0].set_yticklabels([name[:20] + '...' if len(name) > 20 else name for name in top_10_problematic['drug_name']], fontsize=8)
axes[0, 0].set_xlabel('Impact Score')
axes[0, 0].set_title('Top 10 Most Problematic Medications')
axes[0, 0].grid(axis='x', alpha=0.3)

# 2. Branch Performance
top_15_branches = branch_analysis.head(15)
bars = axes[0, 1].bar(range(len(top_15_branches)), top_15_branches['orders_per_branch'], 
                      color=['red' if rate < 70 else 'orange' if rate < 85 else 'green' 
                             for rate in top_15_branches['availability_rate']], alpha=0.7)
axes[0, 1].set_xticks(range(len(top_15_branches)))
axes[0, 1].set_xticklabels([branch[:10] for branch in top_15_branches.index], rotation=45, fontsize=8)
axes[0, 1].set_ylabel('Failed Orders')
axes[0, 1].set_title('Failed Orders by Branch (Color = Availability Rate)')
axes[0, 1].grid(axis='y', alpha=0.3)

# 3. Hourly Pattern
axes[1, 0].plot(hourly_failures.index, hourly_failures['order_number'], marker='o', linewidth=2, markersize=6)
axes[1, 0].set_xlabel('Hour of Day')
axes[1, 0].set_ylabel('Failed Orders')
axes[1, 0].set_title('Failed Orders by Hour of Day')
axes[1, 0].grid(True, alpha=0.3)
axes[1, 0].set_xticks(range(0, 24, 2))

# 4. Availability Status Distribution
status_counts = leads_df['availability_status'].value_counts()
colors = ['red', 'orange', 'lightcoral']
wedges, texts, autotexts = axes[1, 1].pie(status_counts.values, labels=status_counts.index, 
                                          autopct='%1.1f%%', colors=colors, startangle=90)
axes[1, 1].set_title('Order Availability Status Distribution')

plt.tight_layout()
plt.savefig(f'order_loss_dashboard_{ANALYSIS_DATE.replace("-", "")}.png', dpi=300, bbox_inches='tight')
plt.show()

print(f"📊 Dashboard saved as: order_loss_dashboard_{ANALYSIS_DATE.replace('-', '')}.png")

# 🎯 Generate Daily Action Items
print("🎯 DAILY ACTION ITEMS FOR REDUCING ORDER LOSSES")
print("=" * 55)

# 1. Immediate Purchasing Actions
critical_items = purchasing_priorities[purchasing_priorities['urgency'] == 'CRITICAL']
high_priority_items = purchasing_priorities[purchasing_priorities['urgency'] == 'HIGH']

print("\n🚨 IMMEDIATE ACTIONS REQUIRED:")
print(f"1. URGENT RESTOCKING: {len(critical_items)} critical medications need immediate attention")
if len(critical_items) > 0:
    for idx, (code, item) in enumerate(critical_items.head(5).iterrows(), 1):
        print(f"   {idx}. {item['drug_name']} - {item['orders_affected']} orders affected, {item['failure_rate']}% failure rate")

print(f"\n2. HIGH PRIORITY RESTOCKING: {len(high_priority_items)} medications for next-day procurement")
if len(high_priority_items) > 0:
    for idx, (code, item) in enumerate(high_priority_items.head(5).iterrows(), 1):
        print(f"   {idx}. {item['drug_name']} - {item['orders_affected']} orders affected")

# 2. Branch-Specific Actions
print("\n🏪 BRANCH-SPECIFIC ACTIONS:")
low_performing = branch_analysis[branch_analysis['availability_rate'] < 70].head(3)
if len(low_performing) > 0:
    print("   Branches requiring immediate inventory review:")
    for branch, data in low_performing.iterrows():
        print(f"   • {branch}: {data['availability_rate']}% availability, {data['orders_per_branch']} failed orders")

# 3. Operational Improvements
print("\n⚙️ OPERATIONAL IMPROVEMENTS:")
peak_failure_hour = hourly_failures.sort_values('order_number', ascending=False).index[0]
print(f"   • Focus inventory checks before {peak_failure_hour}:00 (peak failure time)")
print(f"   • Review supplier delivery schedules for critical medications")
print(f"   • Implement automated low-stock alerts for top 20 problematic medications")

# 4. Financial Impact
potential_recovery = critical_items['orders_affected'].sum() * avg_order_value * 0.8  # 80% recovery rate
print(f"\n💰 POTENTIAL REVENUE RECOVERY:")
print(f"   • Addressing critical items could recover ~{potential_recovery:,.0f} SAR")
print(f"   • Total daily loss: {estimated_lost_revenue:,} SAR")
print(f"   • Recovery potential: {potential_recovery/estimated_lost_revenue*100:.1f}% of daily losses")

# 📋 Export Business Reports
print("\n📋 EXPORTING BUSINESS REPORTS")
print("=" * 35)

# Create executive summary report
executive_summary = {
    'Analysis_Date': ANALYSIS_DATE,
    'Total_Failed_Orders': total_failed_orders,
    'Estimated_Lost_Revenue_SAR': estimated_lost_revenue,
    'Critical_Medications': len(critical_items),
    'High_Priority_Medications': len(high_priority_items),
    'Overall_Availability_Rate': overall_availability_rate,
    'Branches_Needing_Attention': len(branch_analysis[branch_analysis['availability_rate'] < 70]),
    'Peak_Failure_Hour': peak_failure_hour,
    'Potential_Recovery_SAR': potential_recovery
}

# Save executive summary
pd.DataFrame([executive_summary]).to_csv(f'executive_summary_{ANALYSIS_DATE.replace("-", "")}.csv', index=False)

# Save detailed branch analysis
branch_analysis.to_csv(f'branch_performance_analysis_{ANALYSIS_DATE.replace("-", "")}.csv')

# Save hourly patterns
hourly_failures.to_csv(f'hourly_failure_patterns_{ANALYSIS_DATE.replace("-", "")}.csv')

print("✅ Reports exported:")
print(f"   📊 Executive Summary: executive_summary_{ANALYSIS_DATE.replace('-', '')}.csv")
print(f"   🏪 Branch Analysis: branch_performance_analysis_{ANALYSIS_DATE.replace('-', '')}.csv")
print(f"   ⏰ Hourly Patterns: hourly_failure_patterns_{ANALYSIS_DATE.replace('-', '')}.csv")
print(f"   🎯 Purchasing Recommendations: purchasing_recommendations_{ANALYSIS_DATE.replace('-', '')}.csv")
print(f"   💊 Top Problematic Medications: top_problematic_medications_{ANALYSIS_DATE.replace('-', '')}.csv")
print(f"   📈 Dashboard: order_loss_dashboard_{ANALYSIS_DATE.replace('-', '')}.png")

# 📝 Final Analysis Summary
print("📝 ANALYSIS SUMMARY AND NEXT STEPS")
print("=" * 40)

print(f"\n🔍 ANALYSIS OVERVIEW:")
print(f"   Date: {ANALYSIS_DATE}")
print(f"   Failed Orders Analyzed: {total_failed_orders:,}")
print(f"   Unique Medications: {total_unique_medications:,}")
print(f"   Branches Covered: {leads_df['branch'].nunique()}")

print(f"\n🎯 KEY FINDINGS:")
print(f"   • {unavailable_activities/total_activities*100:.1f}% of activities are unavailable")
print(f"   • {completely_unavailable_orders} orders completely failed due to stock-outs")
print(f"   • {len(critical_items)} medications require immediate restocking")
print(f"   • Peak failure time: {peak_failure_hour}:00")
print(f"   • Estimated daily revenue loss: {estimated_lost_revenue:,} SAR")

print(f"\n🚀 IMMEDIATE NEXT STEPS:")
print(f"   1. Restock {len(critical_items)} critical medications today")
print(f"   2. Review inventory processes at {len(low_performing_branches)} underperforming branches")
print(f"   3. Implement automated alerts for top 20 problematic medications")
print(f"   4. Schedule supplier meetings for consistent stock-out items")
print(f"   5. Run this analysis daily to track improvements")

print(f"\n💡 BUSINESS IMPACT:")
print(f"   • Potential revenue recovery: {potential_recovery:,.0f} SAR ({potential_recovery/estimated_lost_revenue*100:.1f}% of losses)")
print(f"   • Customer satisfaction improvement through reduced order failures")
print(f"   • Operational efficiency gains through data-driven inventory management")

print(f"\n📅 RECOMMENDED SCHEDULE:")
print(f"   • Run this analysis daily at 9:00 AM")
print(f"   • Weekly review of purchasing recommendations")
print(f"   • Monthly branch performance assessment")
print(f"   • Quarterly supplier performance review")

print("\n✅ Analysis Complete! All reports and recommendations have been generated.")