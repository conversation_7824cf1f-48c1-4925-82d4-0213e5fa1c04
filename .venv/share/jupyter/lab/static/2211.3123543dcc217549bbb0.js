"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[2211],{82211:(e,r,n)=>{n.d(r,{Zp:()=>Qt});var t=n(69769);var i=n(92911);var o=0;function a(e){var r=++o;return(0,i.A)(e)+r}const u=a;var s=n(33659);var d=n(74033);var f=n(8937);var c=Math.ceil,v=Math.max;function h(e,r,n,t){var i=-1,o=v(c((r-e)/(n||1)),0),a=Array(o);while(o--){a[t?o:++i]=e;e+=n}return a}const l=h;var g=n(31943);var p=n(52712);function A(e){return function(r,n,t){if(t&&typeof t!="number"&&(0,g.A)(r,n,t)){n=t=undefined}r=(0,p.A)(r);if(n===undefined){n=r;r=0}else{n=(0,p.A)(n)}t=t===undefined?r<n?1:-1:(0,p.A)(t);return l(r,n,t,e)}}const w=A;var b=w();const m=b;var y=n(84416);class E{constructor(){var e={};e._next=e._prev=e;this._sentinel=e}dequeue(){var e=this._sentinel;var r=e._prev;if(r!==e){O(r);return r}}enqueue(e){var r=this._sentinel;if(e._prev&&e._next){O(e)}e._next=r._next;r._next._prev=e;r._next=e;e._prev=r}toString(){var e=[];var r=this._sentinel;var n=r._prev;while(n!==r){e.push(JSON.stringify(n,k));n=n._prev}return"["+e.join(", ")+"]"}}function O(e){e._prev._next=e._next;e._next._prev=e._prev;delete e._next;delete e._prev}function k(e,r){if(e!=="_next"&&e!=="_prev"){return r}}var N=s.A(1);function x(e,r){if(e.nodeCount()<=1){return[]}var n=j(e,r||N);var t=I(n.graph,n.buckets,n.zeroIdx);return d.A(f.A(t,(function(r){return e.outEdges(r.v,r.w)})))}function I(e,r,n){var t=[];var i=r[r.length-1];var o=r[0];var a;while(e.nodeCount()){while(a=o.dequeue()){P(e,r,n,a)}while(a=i.dequeue()){P(e,r,n,a)}if(e.nodeCount()){for(var u=r.length-2;u>0;--u){a=r[u].dequeue();if(a){t=t.concat(P(e,r,n,a,true));break}}}}return t}function P(e,r,n,i,o){var a=o?[]:undefined;t.A(e.inEdges(i.v),(function(t){var i=e.edge(t);var u=e.node(t.v);if(o){a.push({v:t.v,w:t.w})}u.out-=i;C(r,n,u)}));t.A(e.outEdges(i.v),(function(t){var i=e.edge(t);var o=t.w;var a=e.node(o);a["in"]-=i;C(r,n,a)}));e.removeNode(i.v);return a}function j(e,r){var n=new y.T;var i=0;var o=0;t.A(e.nodes(),(function(e){n.setNode(e,{v:e,in:0,out:0})}));t.A(e.edges(),(function(e){var t=n.edge(e.v,e.w)||0;var a=r(e);var u=t+a;n.setEdge(e.v,e.w,u);o=Math.max(o,n.node(e.v).out+=a);i=Math.max(i,n.node(e.w)["in"]+=a)}));var a=m(o+i+3).map((function(){return new E}));var u=i+1;t.A(n.nodes(),(function(e){C(a,u,n.node(e))}));return{graph:n,buckets:a,zeroIdx:u}}function C(e,r,n){if(!n.out){e[0].enqueue(n)}else if(!n["in"]){e[e.length-1].enqueue(n)}else{e[n.out-n["in"]+r].enqueue(n)}}function T(e){var r=e.graph().acyclicer==="greedy"?x(e,n(e)):L(e);t.A(r,(function(r){var n=e.edge(r);e.removeEdge(r);n.forwardName=r.name;n.reversed=true;e.setEdge(r.w,r.v,n,u("rev"))}));function n(e){return function(r){return e.edge(r).weight}}}function L(e){var r=[];var n={};var i={};function o(a){if(Object.prototype.hasOwnProperty.call(i,a)){return}i[a]=true;n[a]=true;t.A(e.outEdges(a),(function(e){if(Object.prototype.hasOwnProperty.call(n,e.w)){r.push(e)}else{o(e.w)}}));delete n[a]}t.A(e.nodes(),o);return r}function M(e){t.A(e.edges(),(function(r){var n=e.edge(r);if(n.reversed){e.removeEdge(r);var t=n.forwardName;delete n.reversed;delete n.forwardName;e.setEdge(r.w,r.v,n,t)}}))}var R=n(96901);var F=n(44835);var S=n(78307);function D(e,r){return(0,F.A)(e,r,(function(r,n){return(0,S.A)(e,n)}))}const V=D;var G=n(27401);var Y=n(4596);function z(e){return(0,Y.A)((0,G.A)(e,undefined,d.A),e+"")}const B=z;var q=B((function(e,r){return e==null?{}:V(e,r)}));const $=q;var Q=n(38693);var J=n(95852);function W(e,r){return e>r}const Z=W;var H=n(63077);function K(e){return e&&e.length?(0,J.A)(e,H.A,Z):undefined}const U=K;var X=n(80359);var ee=n(48657);var re=n(27477);var ne=n(1121);function te(e,r){var n={};r=(0,ne.A)(r,3);(0,re.A)(e,(function(e,t,i){(0,ee.A)(n,t,r(e,t,i))}));return n}const ie=te;var oe=n(89523);var ae=n(963);var ue=n(2850);var se=n(24606);var de=function(){return se.A.Date.now()};const fe=de;function ce(e,r,n,t){var i;do{i=u(t)}while(e.hasNode(i));n.dummy=r;e.setNode(i,n);return i}function ve(e){var r=(new y.T).setGraph(e.graph());t.A(e.nodes(),(function(n){r.setNode(n,e.node(n))}));t.A(e.edges(),(function(n){var t=r.edge(n.v,n.w)||{weight:0,minlen:1};var i=e.edge(n);r.setEdge(n.v,n.w,{weight:t.weight+i.weight,minlen:Math.max(t.minlen,i.minlen)})}));return r}function he(e){var r=new y.T({multigraph:e.isMultigraph()}).setGraph(e.graph());t.A(e.nodes(),(function(n){if(!e.children(n).length){r.setNode(n,e.node(n))}}));t.A(e.edges(),(function(n){r.setEdge(n,e.edge(n))}));return r}function le(e){var r=_.map(e.nodes(),(function(r){var n={};_.forEach(e.outEdges(r),(function(r){n[r.w]=(n[r.w]||0)+e.edge(r).weight}));return n}));return _.zipObject(e.nodes(),r)}function ge(e){var r=_.map(e.nodes(),(function(r){var n={};_.forEach(e.inEdges(r),(function(r){n[r.v]=(n[r.v]||0)+e.edge(r).weight}));return n}));return _.zipObject(e.nodes(),r)}function pe(e,r){var n=e.x;var t=e.y;var i=r.x-n;var o=r.y-t;var a=e.width/2;var u=e.height/2;if(!i&&!o){throw new Error("Not possible to find intersection inside of the rectangle")}var s,d;if(Math.abs(o)*a>Math.abs(i)*u){if(o<0){u=-u}s=u*i/o;d=u}else{if(i<0){a=-a}s=a;d=a*o/i}return{x:n+s,y:t+d}}function Ae(e){var r=f.A(m(ye(e)+1),(function(){return[]}));t.A(e.nodes(),(function(n){var t=e.node(n);var i=t.rank;if(!oe.A(i)){r[i][t.order]=n}}));return r}function we(e){var r=ae.A(f.A(e.nodes(),(function(r){return e.node(r).rank})));t.A(e.nodes(),(function(n){var t=e.node(n);if(ue.A(t,"rank")){t.rank-=r}}))}function be(e){var r=ae.A(f.A(e.nodes(),(function(r){return e.node(r).rank})));var n=[];t.A(e.nodes(),(function(t){var i=e.node(t).rank-r;if(!n[i]){n[i]=[]}n[i].push(t)}));var i=0;var o=e.graph().nodeRankFactor;t.A(n,(function(r,n){if(oe.A(r)&&n%o!==0){--i}else if(i){t.A(r,(function(r){e.node(r).rank+=i}))}}))}function me(e,r,n,t){var i={width:0,height:0};if(arguments.length>=4){i.rank=n;i.order=t}return ce(e,"border",i,r)}function ye(e){return U(f.A(e.nodes(),(function(r){var n=e.node(r).rank;if(!oe.A(n)){return n}})))}function _e(e,r){var n={lhs:[],rhs:[]};t.A(e,(function(e){if(r(e)){n.lhs.push(e)}else{n.rhs.push(e)}}));return n}function Ee(e,r){var n=fe();try{return r()}finally{console.log(e+" time: "+(fe()-n)+"ms")}}function Oe(e,r){return r()}function ke(e){function r(n){var i=e.children(n);var o=e.node(n);if(i.length){t.A(i,r)}if(Object.prototype.hasOwnProperty.call(o,"minRank")){o.borderLeft=[];o.borderRight=[];for(var a=o.minRank,u=o.maxRank+1;a<u;++a){Ne(e,"borderLeft","_bl",n,o,a);Ne(e,"borderRight","_br",n,o,a)}}}t.A(e.children(),r)}function Ne(e,r,n,t,i,o){var a={width:0,height:0,rank:o,borderType:r};var u=i[r][o-1];var s=ce(e,"border",a,n);i[r][o]=s;e.setParent(s,t);if(u){e.setEdge(u,s,{weight:1})}}function xe(e){var r=e.graph().rankdir.toLowerCase();if(r==="lr"||r==="rl"){Pe(e)}}function Ie(e){var r=e.graph().rankdir.toLowerCase();if(r==="bt"||r==="rl"){Ce(e)}if(r==="lr"||r==="rl"){Le(e);Pe(e)}}function Pe(e){t.A(e.nodes(),(function(r){je(e.node(r))}));t.A(e.edges(),(function(r){je(e.edge(r))}))}function je(e){var r=e.width;e.width=e.height;e.height=r}function Ce(e){t.A(e.nodes(),(function(r){Te(e.node(r))}));t.A(e.edges(),(function(r){var n=e.edge(r);t.A(n.points,Te);if(Object.prototype.hasOwnProperty.call(n,"y")){Te(n)}}))}function Te(e){e.y=-e.y}function Le(e){t.A(e.nodes(),(function(r){Me(e.node(r))}));t.A(e.edges(),(function(r){var n=e.edge(r);t.A(n.points,Me);if(Object.prototype.hasOwnProperty.call(n,"x")){Me(n)}}))}function Me(e){var r=e.x;e.x=e.y;e.y=r}function Re(e){e.graph().dummyChains=[];t.A(e.edges(),(function(r){Fe(e,r)}))}function Fe(e,r){var n=r.v;var t=e.node(n).rank;var i=r.w;var o=e.node(i).rank;var a=r.name;var u=e.edge(r);var s=u.labelRank;if(o===t+1)return;e.removeEdge(r);var d=undefined;var f,c;for(c=0,++t;t<o;++c,++t){u.points=[];d={width:0,height:0,edgeLabel:u,edgeObj:r,rank:t};f=ce(e,"edge",d,"_d");if(t===s){d.width=u.width;d.height=u.height;d.dummy="edge-label";d.labelpos=u.labelpos}e.setEdge(n,f,{weight:u.weight},a);if(c===0){e.graph().dummyChains.push(f)}n=f}e.setEdge(n,i,{weight:u.weight},a)}function Se(e){t.A(e.graph().dummyChains,(function(r){var n=e.node(r);var t=n.edgeLabel;var i;e.setEdge(n.edgeObj,t);while(n.dummy){i=e.successors(r)[0];e.removeNode(r);t.points.push({x:n.x,y:n.y});if(n.dummy==="edge-label"){t.x=n.x;t.y=n.y;t.width=n.width;t.height=n.height}r=i;n=e.node(r)}}))}var De=n(51135);function Ve(e,r){return e&&e.length?(0,J.A)(e,(0,ne.A)(r,2),De.A):undefined}const Ge=Ve;function Ye(e){var r={};function n(t){var i=e.node(t);if(Object.prototype.hasOwnProperty.call(r,t)){return i.rank}r[t]=true;var o=ae.A(f.A(e.outEdges(t),(function(r){return n(r.w)-e.edge(r).minlen})));if(o===Number.POSITIVE_INFINITY||o===undefined||o===null){o=0}return i.rank=o}t.A(e.sources(),n)}function ze(e,r){return e.node(r.w).rank-e.node(r.v).rank-e.edge(r).minlen}function Be(e){var r=new y.T({directed:false});var n=e.nodes()[0];var t=e.nodeCount();r.setNode(n,{});var i,o;while(qe(r,e)<t){i=$e(r,e);o=r.hasNode(i.v)?ze(e,i):-ze(e,i);Qe(r,e,o)}return r}function qe(e,r){function n(i){t.A(r.nodeEdges(i),(function(t){var o=t.v,a=i===o?t.w:o;if(!e.hasNode(a)&&!ze(r,t)){e.setNode(a,{});e.setEdge(i,a,{});n(a)}}))}t.A(e.nodes(),n);return e.nodeCount()}function $e(e,r){return Ge(r.edges(),(function(n){if(e.hasNode(n.v)!==e.hasNode(n.w)){return ze(r,n)}}))}function Qe(e,r,n){t.A(e.nodes(),(function(e){r.node(e).rank+=n}))}var Je=n(85075);var We=n(97133);var Ze=s.A(1);function He(e,r,n,t){return Ke(e,String(r),n||Ze,t||function(r){return e.outEdges(r)})}function Ke(e,r,n,t){var i={};var o=new PriorityQueue;var a,u;var s=function(e){var r=e.v!==a?e.v:e.w;var t=i[r];var s=n(e);var d=u.distance+s;if(s<0){throw new Error("dijkstra does not allow negative edge weights. "+"Bad edge: "+e+" Weight: "+s)}if(d<t.distance){t.distance=d;t.predecessor=a;o.decrease(r,d)}};e.nodes().forEach((function(e){var n=e===r?0:Number.POSITIVE_INFINITY;i[e]={distance:n};o.add(e,n)}));while(o.size()>0){a=o.removeMin();u=i[a];if(u.distance===Number.POSITIVE_INFINITY){break}t(a).forEach(s)}return i}function Ue(e,r,n){return _.transform(e.nodes(),(function(t,i){t[i]=dijkstra(e,i,r,n)}),{})}var Xe=s.A(1);function er(e,r,n){return rr(e,r||Xe,n||function(r){return e.outEdges(r)})}function rr(e,r,n){var t={};var i=e.nodes();i.forEach((function(e){t[e]={};t[e][e]={distance:0};i.forEach((function(r){if(e!==r){t[e][r]={distance:Number.POSITIVE_INFINITY}}}));n(e).forEach((function(n){var i=n.v===e?n.w:n.v;var o=r(n);t[e][i]={distance:o,predecessor:e}}))}));i.forEach((function(e){var r=t[e];i.forEach((function(n){var o=t[n];i.forEach((function(n){var t=o[e];var i=r[n];var a=o[n];var u=t.distance+i.distance;if(u<a.distance){a.distance=u;a.predecessor=i.predecessor}}))}))}));return t}var nr=n(30568);var tr=n(88753);var ir=n(21585);var or=n(86378);var ar=n(43162);var ur=(0,ar.A)("length");const sr=ur;var dr="\\ud800-\\udfff",fr="\\u0300-\\u036f",cr="\\ufe20-\\ufe2f",vr="\\u20d0-\\u20ff",hr=fr+cr+vr,lr="\\ufe0e\\ufe0f";var gr="\\u200d";var pr=RegExp("["+gr+dr+hr+lr+"]");function Ar(e){return pr.test(e)}const wr=Ar;var br="\\ud800-\\udfff",mr="\\u0300-\\u036f",yr="\\ufe20-\\ufe2f",_r="\\u20d0-\\u20ff",Er=mr+yr+_r,Or="\\ufe0e\\ufe0f";var kr="["+br+"]",Nr="["+Er+"]",xr="\\ud83c[\\udffb-\\udfff]",Ir="(?:"+Nr+"|"+xr+")",Pr="[^"+br+"]",jr="(?:\\ud83c[\\udde6-\\uddff]){2}",Cr="[\\ud800-\\udbff][\\udc00-\\udfff]",Tr="\\u200d";var Lr=Ir+"?",Mr="["+Or+"]?",Rr="(?:"+Tr+"(?:"+[Pr,jr,Cr].join("|")+")"+Mr+Lr+")*",Fr=Mr+Lr+Rr,Sr="(?:"+[Pr+Nr+"?",Nr,jr,Cr,kr].join("|")+")";var Dr=RegExp(xr+"(?="+xr+")|"+Sr+Fr,"g");function Vr(e){var r=Dr.lastIndex=0;while(Dr.test(e)){++r}return r}const Gr=Vr;function Yr(e){return wr(e)?Gr(e):sr(e)}const zr=Yr;var Br="[object Map]",qr="[object Set]";function $r(e){if(e==null){return 0}if((0,ir.A)(e)){return(0,or.A)(e)?zr(e):e.length}var r=(0,tr.A)(e);if(r==Br||r==qr){return e.size}return(0,nr.A)(e).length}const Qr=$r;Jr.CycleException=Wr;function Jr(e){var r={};var n={};var i=[];function o(a){if(Object.prototype.hasOwnProperty.call(n,a)){throw new Wr}if(!Object.prototype.hasOwnProperty.call(r,a)){n[a]=true;r[a]=true;t.A(e.predecessors(a),o);delete n[a];i.push(a)}}t.A(e.sinks(),o);if(Qr(r)!==e.nodeCount()){throw new Wr}return i}function Wr(){}Wr.prototype=new Error;function Zr(e){try{topsort(e)}catch(r){if(r instanceof CycleException){return false}throw r}return true}var Hr=n(39990);function Kr(e,r,n){if(!Hr.A(r)){r=[r]}var i=(e.isDirected()?e.successors:e.neighbors).bind(e);var o=[];var a={};t.A(r,(function(r){if(!e.hasNode(r)){throw new Error("Graph does not have node: "+r)}Ur(e,r,n==="post",a,i,o)}));return o}function Ur(e,r,n,i,o,a){if(!Object.prototype.hasOwnProperty.call(i,r)){i[r]=true;if(!n){a.push(r)}t.A(o(r),(function(r){Ur(e,r,n,i,o,a)}));if(n){a.push(r)}}}function Xr(e,r){return Kr(e,r,"post")}function en(e,r){return Kr(e,r,"pre")}var rn=n(65791);function nn(e,r){var n=new Graph;var t={};var i=new PriorityQueue;var o;function a(e){var n=e.v===o?e.w:e.v;var a=i.priority(n);if(a!==undefined){var u=r(e);if(u<a){t[n]=o;i.decrease(n,u)}}}if(e.nodeCount()===0){return n}_.each(e.nodes(),(function(e){i.add(e,Number.POSITIVE_INFINITY);n.setNode(e)}));i.decrease(e.nodes()[0],0);var u=false;while(i.size()>0){o=i.removeMin();if(Object.prototype.hasOwnProperty.call(t,o)){n.setEdge(o,t[o])}else if(u){throw new Error("Input graph is not connected: "+e)}else{u=true}e.nodeEdges(o).forEach(a)}return n}tn.initLowLimValues=sn;tn.initCutValues=on;tn.calcCutValue=un;tn.leaveEdge=fn;tn.enterEdge=cn;tn.exchangeEdges=vn;function tn(e){e=ve(e);Ye(e);var r=Be(e);sn(r);on(r,e);var n,t;while(n=fn(r)){t=cn(r,e,n);vn(r,e,n,t)}}function on(e,r){var n=Xr(e,e.nodes());n=n.slice(0,n.length-1);t.A(n,(function(n){an(e,r,n)}))}function an(e,r,n){var t=e.node(n);var i=t.parent;e.edge(n,i).cutvalue=un(e,r,n)}function un(e,r,n){var i=e.node(n);var o=i.parent;var a=true;var u=r.edge(n,o);var s=0;if(!u){a=false;u=r.edge(o,n)}s=u.weight;t.A(r.nodeEdges(n),(function(t){var i=t.v===n,u=i?t.w:t.v;if(u!==o){var d=i===a,f=r.edge(t).weight;s+=d?f:-f;if(ln(e,n,u)){var c=e.edge(n,u).cutvalue;s+=d?-c:c}}}));return s}function sn(e,r){if(arguments.length<2){r=e.nodes()[0]}dn(e,{},1,r)}function dn(e,r,n,i,o){var a=n;var u=e.node(i);r[i]=true;t.A(e.neighbors(i),(function(t){if(!Object.prototype.hasOwnProperty.call(r,t)){n=dn(e,r,n,t,i)}}));u.low=a;u.lim=n++;if(o){u.parent=o}else{delete u.parent}return n}function fn(e){return Je.A(e.edges(),(function(r){return e.edge(r).cutvalue<0}))}function cn(e,r,n){var t=n.v;var i=n.w;if(!r.hasEdge(t,i)){t=n.w;i=n.v}var o=e.node(t);var a=e.node(i);var u=o;var s=false;if(o.lim>a.lim){u=a;s=true}var d=We.A(r.edges(),(function(r){return s===gn(e,e.node(r.v),u)&&s!==gn(e,e.node(r.w),u)}));return Ge(d,(function(e){return ze(r,e)}))}function vn(e,r,n,t){var i=n.v;var o=n.w;e.removeEdge(i,o);e.setEdge(t.v,t.w,{});sn(e);on(e,r);hn(e,r)}function hn(e,r){var n=Je.A(e.nodes(),(function(e){return!r.node(e).parent}));var i=en(e,n);i=i.slice(1);t.A(i,(function(n){var t=e.node(n).parent,i=r.edge(n,t),o=false;if(!i){i=r.edge(t,n);o=true}r.node(n).rank=r.node(t).rank+(o?i.minlen:-i.minlen)}))}function ln(e,r,n){return e.hasEdge(r,n)}function gn(e,r,n){return n.low<=r.lim&&r.lim<=n.lim}function pn(e){switch(e.graph().ranker){case"network-simplex":bn(e);break;case"tight-tree":wn(e);break;case"longest-path":An(e);break;default:bn(e)}}var An=Ye;function wn(e){Ye(e);Be(e)}function bn(e){tn(e)}var mn=n(44882);var yn=n(65339);function _n(e){var r=ce(e,"root",{},"_root");var n=On(e);var i=U(mn.A(n))-1;var o=2*i+1;e.graph().nestingRoot=r;t.A(e.edges(),(function(r){e.edge(r).minlen*=o}));var a=kn(e)+1;t.A(e.children(),(function(t){En(e,r,o,a,i,n,t)}));e.graph().nodeRankFactor=o}function En(e,r,n,i,o,a,u){var s=e.children(u);if(!s.length){if(u!==r){e.setEdge(r,u,{weight:0,minlen:n})}return}var d=me(e,"_bt");var f=me(e,"_bb");var c=e.node(u);e.setParent(d,u);c.borderTop=d;e.setParent(f,u);c.borderBottom=f;t.A(s,(function(t){En(e,r,n,i,o,a,t);var s=e.node(t);var c=s.borderTop?s.borderTop:t;var v=s.borderBottom?s.borderBottom:t;var h=s.borderTop?i:2*i;var l=c!==v?1:o-a[u]+1;e.setEdge(d,c,{weight:h,minlen:l,nestingEdge:true});e.setEdge(v,f,{weight:h,minlen:l,nestingEdge:true})}));if(!e.parent(u)){e.setEdge(r,d,{weight:0,minlen:o+a[u]})}}function On(e){var r={};function n(i,o){var a=e.children(i);if(a&&a.length){t.A(a,(function(e){n(e,o+1)}))}r[i]=o}t.A(e.children(),(function(e){n(e,1)}));return r}function kn(e){return yn.A(e.edges(),(function(r,n){return r+e.edge(n).weight}),0)}function Nn(e){var r=e.graph();e.removeNode(r.nestingRoot);delete r.nestingRoot;t.A(e.edges(),(function(r){var n=e.edge(r);if(n.nestingEdge){e.removeEdge(r)}}))}var xn=n(59386);var In=1,Pn=4;function jn(e){return(0,xn.A)(e,In|Pn)}const Cn=jn;function Tn(e,r,n){var i={},o;t.A(n,(function(n){var t=e.parent(n),a,u;while(t){a=e.parent(t);if(a){u=i[a];i[a]=t}else{u=o;o=t}if(u&&u!==t){r.setEdge(u,t);return}t=a}}))}function Ln(e,r,n){var i=Mn(e),o=new y.T({compound:true}).setGraph({root:i}).setDefaultNodeLabel((function(r){return e.node(r)}));t.A(e.nodes(),(function(a){var u=e.node(a),s=e.parent(a);if(u.rank===r||u.minRank<=r&&r<=u.maxRank){o.setNode(a);o.setParent(a,s||i);t.A(e[n](a),(function(r){var n=r.v===a?r.w:r.v,t=o.edge(n,a),i=!oe.A(t)?t.weight:0;o.setEdge(n,a,{weight:e.edge(r).weight+i})}));if(Object.prototype.hasOwnProperty.call(u,"minRank")){o.setNode(a,{borderLeft:u.borderLeft[r],borderRight:u.borderRight[r]})}}}));return o}function Mn(e){var r;while(e.hasNode(r=u("_root")));return r}var Rn=n(16542);function Fn(e,r,n){var t=-1,i=e.length,o=r.length,a={};while(++t<i){var u=t<o?r[t]:undefined;n(a,e[t],u)}return a}const Sn=Fn;function Dn(e,r){return Sn(e||[],r||[],Rn.A)}const Vn=Dn;var Gn=n(62040);var Yn=n(98519);var zn=n(22883);var Bn=n(97457);function qn(e,r){var n=e.length;e.sort(r);while(n--){e[n]=e[n].value}return e}const $n=qn;var Qn=n(26132);var Jn=n(62579);function Wn(e,r){if(e!==r){var n=e!==undefined,t=e===null,i=e===e,o=(0,Jn.A)(e);var a=r!==undefined,u=r===null,s=r===r,d=(0,Jn.A)(r);if(!u&&!d&&!o&&e>r||o&&a&&s&&!u&&!d||t&&a&&s||!n&&s||!i){return 1}if(!t&&!o&&!d&&e<r||d&&n&&i&&!t&&!o||u&&n&&i||!a&&i||!s){return-1}}return 0}const Zn=Wn;function Hn(e,r,n){var t=-1,i=e.criteria,o=r.criteria,a=i.length,u=n.length;while(++t<a){var s=Zn(i[t],o[t]);if(s){if(t>=u){return s}var d=n[t];return s*(d=="desc"?-1:1)}}return e.index-r.index}const Kn=Hn;function Un(e,r,n){if(r.length){r=(0,Yn.A)(r,(function(e){if((0,Hr.A)(e)){return function(r){return(0,zn.A)(r,e.length===1?e[0]:e)}}return e}))}else{r=[H.A]}var t=-1;r=(0,Yn.A)(r,(0,Qn.A)(ne.A));var i=(0,Bn.A)(e,(function(e,n,i){var o=(0,Yn.A)(r,(function(r){return r(e)}));return{criteria:o,index:++t,value:e}}));return $n(i,(function(e,r){return Kn(e,r,n)}))}const Xn=Un;var et=n(55881);var rt=(0,et.A)((function(e,r){if(e==null){return[]}var n=r.length;if(n>1&&(0,g.A)(e,r[0],r[1])){r=[]}else if(n>2&&(0,g.A)(r[0],r[1],r[2])){r=[r[0]]}return Xn(e,(0,Gn.A)(r,1),[])}));const nt=rt;function tt(e,r){var n=0;for(var t=1;t<r.length;++t){n+=it(e,r[t-1],r[t])}return n}function it(e,r,n){var i=Vn(n,f.A(n,(function(e,r){return r})));var o=d.A(f.A(r,(function(r){return nt(f.A(e.outEdges(r),(function(r){return{pos:i[r.w],weight:e.edge(r).weight}})),"pos")})));var a=1;while(a<n.length)a<<=1;var u=2*a-1;a-=1;var s=f.A(new Array(u),(function(){return 0}));var c=0;t.A(o.forEach((function(e){var r=e.pos+a;s[r]+=e.weight;var n=0;while(r>0){if(r%2){n+=s[r+1]}r=r-1>>1;s[r]+=e.weight}c+=e.weight*n})));return c}function ot(e){var r={};var n=We.A(e.nodes(),(function(r){return!e.children(r).length}));var i=U(f.A(n,(function(r){return e.node(r).rank})));var o=f.A(m(i+1),(function(){return[]}));function a(n){if(ue.A(r,n))return;r[n]=true;var i=e.node(n);o[i.rank].push(n);t.A(e.successors(n),a)}var u=nt(n,(function(r){return e.node(r).rank}));t.A(u,a);return o}function at(e,r){return f.A(r,(function(r){var n=e.inEdges(r);if(!n.length){return{v:r}}else{var t=yn.A(n,(function(r,n){var t=e.edge(n),i=e.node(n.v);return{sum:r.sum+t.weight*i.order,weight:r.weight+t.weight}}),{sum:0,weight:0});return{v:r,barycenter:t.sum/t.weight,weight:t.weight}}}))}function ut(e,r){var n={};t.A(e,(function(e,r){var t=n[e.v]={indegree:0,in:[],out:[],vs:[e.v],i:r};if(!oe.A(e.barycenter)){t.barycenter=e.barycenter;t.weight=e.weight}}));t.A(r.edges(),(function(e){var r=n[e.v];var t=n[e.w];if(!oe.A(r)&&!oe.A(t)){t.indegree++;r.out.push(n[e.w])}}));var i=We.A(n,(function(e){return!e.indegree}));return st(i)}function st(e){var r=[];function n(e){return function(r){if(r.merged){return}if(oe.A(r.barycenter)||oe.A(e.barycenter)||r.barycenter>=e.barycenter){dt(e,r)}}}function i(r){return function(n){n["in"].push(r);if(--n.indegree===0){e.push(n)}}}while(e.length){var o=e.pop();r.push(o);t.A(o["in"].reverse(),n(o));t.A(o.out,i(o))}return f.A(We.A(r,(function(e){return!e.merged})),(function(e){return $(e,["vs","i","barycenter","weight"])}))}function dt(e,r){var n=0;var t=0;if(e.weight){n+=e.barycenter*e.weight;t+=e.weight}if(r.weight){n+=r.barycenter*r.weight;t+=r.weight}e.vs=r.vs.concat(e.vs);e.barycenter=n/t;e.weight=t;e.i=Math.min(r.i,e.i);r.merged=true}function ft(e,r){var n=_e(e,(function(e){return Object.prototype.hasOwnProperty.call(e,"barycenter")}));var i=n.lhs,o=nt(n.rhs,(function(e){return-e.i})),a=[],u=0,s=0,f=0;i.sort(vt(!!r));f=ct(a,o,f);t.A(i,(function(e){f+=e.vs.length;a.push(e.vs);u+=e.barycenter*e.weight;s+=e.weight;f=ct(a,o,f)}));var c={vs:d.A(a)};if(s){c.barycenter=u/s;c.weight=s}return c}function ct(e,r,n){var t;while(r.length&&(t=X.A(r)).i<=n){r.pop();e.push(t.vs);n++}return n}function vt(e){return function(r,n){if(r.barycenter<n.barycenter){return-1}else if(r.barycenter>n.barycenter){return 1}return!e?r.i-n.i:n.i-r.i}}function ht(e,r,n,i){var o=e.children(r);var a=e.node(r);var u=a?a.borderLeft:undefined;var s=a?a.borderRight:undefined;var f={};if(u){o=We.A(o,(function(e){return e!==u&&e!==s}))}var c=at(e,o);t.A(c,(function(r){if(e.children(r.v).length){var t=ht(e,r.v,n,i);f[r.v]=t;if(Object.prototype.hasOwnProperty.call(t,"barycenter")){gt(r,t)}}}));var v=ut(c,n);lt(v,f);var h=ft(v,i);if(u){h.vs=d.A([u,h.vs,s]);if(e.predecessors(u).length){var l=e.node(e.predecessors(u)[0]),g=e.node(e.predecessors(s)[0]);if(!Object.prototype.hasOwnProperty.call(h,"barycenter")){h.barycenter=0;h.weight=0}h.barycenter=(h.barycenter*h.weight+l.order+g.order)/(h.weight+2);h.weight+=2}}return h}function lt(e,r){t.A(e,(function(e){e.vs=d.A(e.vs.map((function(e){if(r[e]){return r[e].vs}return e})))}))}function gt(e,r){if(!oe.A(e.barycenter)){e.barycenter=(e.barycenter*e.weight+r.barycenter*r.weight)/(e.weight+r.weight);e.weight+=r.weight}else{e.barycenter=r.barycenter;e.weight=r.weight}}function pt(e){var r=ye(e),n=At(e,m(1,r+1),"inEdges"),t=At(e,m(r-1,-1,-1),"outEdges");var i=ot(e);bt(e,i);var o=Number.POSITIVE_INFINITY,a;for(var u=0,s=0;s<4;++u,++s){wt(u%2?n:t,u%4>=2);i=Ae(e);var d=tt(e,i);if(d<o){s=0;a=Cn(i);o=d}}bt(e,a)}function At(e,r,n){return f.A(r,(function(r){return Ln(e,r,n)}))}function wt(e,r){var n=new y.T;t.A(e,(function(e){var i=e.graph().root;var o=ht(e,i,n,r);t.A(o.vs,(function(r,n){e.node(r).order=n}));Tn(e,n,o.vs)}))}function bt(e,r){t.A(r,(function(r){t.A(r,(function(r,n){e.node(r).order=n}))}))}function mt(e){var r=_t(e);t.A(e.graph().dummyChains,(function(n){var t=e.node(n);var i=t.edgeObj;var o=yt(e,r,i.v,i.w);var a=o.path;var u=o.lca;var s=0;var d=a[s];var f=true;while(n!==i.w){t=e.node(n);if(f){while((d=a[s])!==u&&e.node(d).maxRank<t.rank){s++}if(d===u){f=false}}if(!f){while(s<a.length-1&&e.node(d=a[s+1]).minRank<=t.rank){s++}d=a[s]}e.setParent(n,d);n=e.successors(n)[0]}}))}function yt(e,r,n,t){var i=[];var o=[];var a=Math.min(r[n].low,r[t].low);var u=Math.max(r[n].lim,r[t].lim);var s;var d;s=n;do{s=e.parent(s);i.push(s)}while(s&&(r[s].low>a||u>r[s].lim));d=s;s=t;while((s=e.parent(s))!==d){o.push(s)}return{path:i.concat(o.reverse()),lca:d}}function _t(e){var r={};var n=0;function i(o){var a=n;t.A(e.children(o),i);r[o]={low:a,lim:n++}}t.A(e.children(),i);return r}var Et=n(76253);function Ot(e,r){return e&&(0,re.A)(e,(0,Et.A)(r))}const kt=Ot;var Nt=n(40283);var xt=n(13839);function It(e,r){return e==null?e:(0,Nt.A)(e,(0,Et.A)(r),xt.A)}const Pt=It;function jt(e,r){var n={};function i(r,i){var o=0,a=0,u=r.length,s=X.A(i);t.A(i,(function(r,d){var f=Tt(e,r),c=f?e.node(f).order:u;if(f||r===s){t.A(i.slice(a,d+1),(function(r){t.A(e.predecessors(r),(function(t){var i=e.node(t),a=i.order;if((a<o||c<a)&&!(i.dummy&&e.node(r).dummy)){Lt(n,t,r)}}))}));a=d+1;o=c}}));return i}yn.A(r,i);return n}function Ct(e,r){var n={};function i(r,i,o,a,u){var s;t.A(m(i,o),(function(i){s=r[i];if(e.node(s).dummy){t.A(e.predecessors(s),(function(r){var t=e.node(r);if(t.dummy&&(t.order<a||t.order>u)){Lt(n,r,s)}}))}}))}function o(r,n){var o=-1,a,u=0;t.A(n,(function(t,s){if(e.node(t).dummy==="border"){var d=e.predecessors(t);if(d.length){a=e.node(d[0]).order;i(n,u,s,o,a);u=s;o=a}}i(n,u,n.length,a,r.length)}));return n}yn.A(r,o);return n}function Tt(e,r){if(e.node(r).dummy){return Je.A(e.predecessors(r),(function(r){return e.node(r).dummy}))}}function Lt(e,r,n){if(r>n){var t=r;r=n;n=t}var i=e[r];if(!i){e[r]=i={}}i[n]=true}function Mt(e,r,n){if(r>n){var t=r;r=n;n=t}return!!e[r]&&Object.prototype.hasOwnProperty.call(e[r],n)}function Rt(e,r,n,i){var o={},a={},u={};t.A(r,(function(e){t.A(e,(function(e,r){o[e]=e;a[e]=e;u[e]=r}))}));t.A(r,(function(e){var r=-1;t.A(e,(function(e){var t=i(e);if(t.length){t=nt(t,(function(e){return u[e]}));var s=(t.length-1)/2;for(var d=Math.floor(s),f=Math.ceil(s);d<=f;++d){var c=t[d];if(a[e]===e&&r<u[c]&&!Mt(n,e,c)){a[c]=e;a[e]=o[e]=o[c];r=u[c]}}}}))}));return{root:o,align:a}}function Ft(e,r,n,i,o){var a={},u=St(e,r,n,o),s=o?"borderLeft":"borderRight";function d(e,r){var n=u.nodes();var t=n.pop();var i={};while(t){if(i[t]){e(t)}else{i[t]=true;n.push(t);n=n.concat(r(t))}t=n.pop()}}function f(e){a[e]=u.inEdges(e).reduce((function(e,r){return Math.max(e,a[r.v]+u.edge(r))}),0)}function c(r){var n=u.outEdges(r).reduce((function(e,r){return Math.min(e,a[r.w]-u.edge(r))}),Number.POSITIVE_INFINITY);var t=e.node(r);if(n!==Number.POSITIVE_INFINITY&&t.borderType!==s){a[r]=Math.max(a[r],n)}}d(f,u.predecessors.bind(u));d(c,u.successors.bind(u));t.A(i,(function(e){a[e]=a[n[e]]}));return a}function St(e,r,n,i){var o=new y.T,a=e.graph(),u=zt(a.nodesep,a.edgesep,i);t.A(r,(function(r){var i;t.A(r,(function(r){var t=n[r];o.setNode(t);if(i){var a=n[i],s=o.edge(a,t);o.setEdge(a,t,Math.max(u(e,r,i),s||0))}i=r}))}));return o}function Dt(e,r){return Ge(mn.A(r),(function(r){var n=Number.NEGATIVE_INFINITY;var t=Number.POSITIVE_INFINITY;Pt(r,(function(r,i){var o=Bt(e,i)/2;n=Math.max(r+o,n);t=Math.min(r-o,t)}));return n-t}))}function Vt(e,r){var n=mn.A(r),i=ae.A(n),o=U(n);t.A(["u","d"],(function(n){t.A(["l","r"],(function(t){var a=n+t,u=e[a],s;if(u===r)return;var d=mn.A(u);s=t==="l"?i-ae.A(d):o-U(d);if(s){e[a]=ie(u,(function(e){return e+s}))}}))}))}function Gt(e,r){return ie(e.ul,(function(n,t){if(r){return e[r.toLowerCase()][t]}else{var i=nt(f.A(e,t));return(i[1]+i[2])/2}}))}function Yt(e){var r=Ae(e);var n=R.A(jt(e,r),Ct(e,r));var i={};var o;t.A(["u","d"],(function(a){o=a==="u"?r:mn.A(r).reverse();t.A(["l","r"],(function(r){if(r==="r"){o=f.A(o,(function(e){return mn.A(e).reverse()}))}var t=(a==="u"?e.predecessors:e.successors).bind(e);var u=Rt(e,o,n,t);var s=Ft(e,o,u.root,u.align,r==="r");if(r==="r"){s=ie(s,(function(e){return-e}))}i[a+r]=s}))}));var a=Dt(e,i);Vt(i,a);return Gt(i,e.graph().align)}function zt(e,r,n){return function(t,i,o){var a=t.node(i);var u=t.node(o);var s=0;var d;s+=a.width/2;if(Object.prototype.hasOwnProperty.call(a,"labelpos")){switch(a.labelpos.toLowerCase()){case"l":d=-a.width/2;break;case"r":d=a.width/2;break}}if(d){s+=n?d:-d}d=0;s+=(a.dummy?r:e)/2;s+=(u.dummy?r:e)/2;s+=u.width/2;if(Object.prototype.hasOwnProperty.call(u,"labelpos")){switch(u.labelpos.toLowerCase()){case"l":d=u.width/2;break;case"r":d=-u.width/2;break}}if(d){s+=n?d:-d}d=0;return s}}function Bt(e,r){return e.node(r).width}function qt(e){e=he(e);$t(e);kt(Yt(e),(function(r,n){e.node(n).x=r}))}function $t(e){var r=Ae(e);var n=e.graph().ranksep;var i=0;t.A(r,(function(r){var o=U(f.A(r,(function(r){return e.node(r).height})));t.A(r,(function(r){e.node(r).y=i+o/2}));i+=o+n}))}function Qt(e,r){var n=r&&r.debugTiming?Ee:Oe;n("layout",(()=>{var r=n("  buildLayoutGraph",(()=>ti(e)));n("  runLayout",(()=>Jt(r,n)));n("  updateInputGraph",(()=>Wt(e,r)))}))}function Jt(e,r){r("    makeSpaceForEdgeLabels",(()=>ii(e)));r("    removeSelfEdges",(()=>hi(e)));r("    acyclic",(()=>T(e)));r("    nestingGraph.run",(()=>_n(e)));r("    rank",(()=>pn(he(e))));r("    injectEdgeLabelProxies",(()=>oi(e)));r("    removeEmptyRanks",(()=>be(e)));r("    nestingGraph.cleanup",(()=>Nn(e)));r("    normalizeRanks",(()=>we(e)));r("    assignRankMinMax",(()=>ai(e)));r("    removeEdgeLabelProxies",(()=>ui(e)));r("    normalize.run",(()=>Re(e)));r("    parentDummyChains",(()=>mt(e)));r("    addBorderSegments",(()=>ke(e)));r("    order",(()=>pt(e)));r("    insertSelfEdges",(()=>li(e)));r("    adjustCoordinateSystem",(()=>xe(e)));r("    position",(()=>qt(e)));r("    positionSelfEdges",(()=>gi(e)));r("    removeBorderNodes",(()=>vi(e)));r("    normalize.undo",(()=>Se(e)));r("    fixupEdgeLabelCoords",(()=>fi(e)));r("    undoCoordinateSystem",(()=>Ie(e)));r("    translateGraph",(()=>si(e)));r("    assignNodeIntersects",(()=>di(e)));r("    reversePoints",(()=>ci(e)));r("    acyclic.undo",(()=>M(e)))}function Wt(e,r){t.A(e.nodes(),(function(n){var t=e.node(n);var i=r.node(n);if(t){t.x=i.x;t.y=i.y;if(r.children(n).length){t.width=i.width;t.height=i.height}}}));t.A(e.edges(),(function(n){var t=e.edge(n);var i=r.edge(n);t.points=i.points;if(Object.prototype.hasOwnProperty.call(i,"x")){t.x=i.x;t.y=i.y}}));e.graph().width=r.graph().width;e.graph().height=r.graph().height}var Zt=["nodesep","edgesep","ranksep","marginx","marginy"];var Ht={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"};var Kt=["acyclicer","ranker","rankdir","align"];var Ut=["width","height"];var Xt={width:0,height:0};var ei=["minlen","weight","width","height","labeloffset"];var ri={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"};var ni=["labelpos"];function ti(e){var r=new y.T({multigraph:true,compound:true});var n=Ai(e.graph());r.setGraph(R.A({},Ht,pi(n,Zt),$(n,Kt)));t.A(e.nodes(),(function(n){var t=Ai(e.node(n));r.setNode(n,Q.A(pi(t,Ut),Xt));r.setParent(n,e.parent(n))}));t.A(e.edges(),(function(n){var t=Ai(e.edge(n));r.setEdge(n,R.A({},ri,pi(t,ei),$(t,ni)))}));return r}function ii(e){var r=e.graph();r.ranksep/=2;t.A(e.edges(),(function(n){var t=e.edge(n);t.minlen*=2;if(t.labelpos.toLowerCase()!=="c"){if(r.rankdir==="TB"||r.rankdir==="BT"){t.width+=t.labeloffset}else{t.height+=t.labeloffset}}}))}function oi(e){t.A(e.edges(),(function(r){var n=e.edge(r);if(n.width&&n.height){var t=e.node(r.v);var i=e.node(r.w);var o={rank:(i.rank-t.rank)/2+t.rank,e:r};ce(e,"edge-proxy",o,"_ep")}}))}function ai(e){var r=0;t.A(e.nodes(),(function(n){var t=e.node(n);if(t.borderTop){t.minRank=e.node(t.borderTop).rank;t.maxRank=e.node(t.borderBottom).rank;r=U(r,t.maxRank)}}));e.graph().maxRank=r}function ui(e){t.A(e.nodes(),(function(r){var n=e.node(r);if(n.dummy==="edge-proxy"){e.edge(n.e).labelRank=n.rank;e.removeNode(r)}}))}function si(e){var r=Number.POSITIVE_INFINITY;var n=0;var i=Number.POSITIVE_INFINITY;var o=0;var a=e.graph();var u=a.marginx||0;var s=a.marginy||0;function d(e){var t=e.x;var a=e.y;var u=e.width;var s=e.height;r=Math.min(r,t-u/2);n=Math.max(n,t+u/2);i=Math.min(i,a-s/2);o=Math.max(o,a+s/2)}t.A(e.nodes(),(function(r){d(e.node(r))}));t.A(e.edges(),(function(r){var n=e.edge(r);if(Object.prototype.hasOwnProperty.call(n,"x")){d(n)}}));r-=u;i-=s;t.A(e.nodes(),(function(n){var t=e.node(n);t.x-=r;t.y-=i}));t.A(e.edges(),(function(n){var o=e.edge(n);t.A(o.points,(function(e){e.x-=r;e.y-=i}));if(Object.prototype.hasOwnProperty.call(o,"x")){o.x-=r}if(Object.prototype.hasOwnProperty.call(o,"y")){o.y-=i}}));a.width=n-r+u;a.height=o-i+s}function di(e){t.A(e.edges(),(function(r){var n=e.edge(r);var t=e.node(r.v);var i=e.node(r.w);var o,a;if(!n.points){n.points=[];o=i;a=t}else{o=n.points[0];a=n.points[n.points.length-1]}n.points.unshift(pe(t,o));n.points.push(pe(i,a))}))}function fi(e){t.A(e.edges(),(function(r){var n=e.edge(r);if(Object.prototype.hasOwnProperty.call(n,"x")){if(n.labelpos==="l"||n.labelpos==="r"){n.width-=n.labeloffset}switch(n.labelpos){case"l":n.x-=n.width/2+n.labeloffset;break;case"r":n.x+=n.width/2+n.labeloffset;break}}}))}function ci(e){t.A(e.edges(),(function(r){var n=e.edge(r);if(n.reversed){n.points.reverse()}}))}function vi(e){t.A(e.nodes(),(function(r){if(e.children(r).length){var n=e.node(r);var t=e.node(n.borderTop);var i=e.node(n.borderBottom);var o=e.node(X.A(n.borderLeft));var a=e.node(X.A(n.borderRight));n.width=Math.abs(a.x-o.x);n.height=Math.abs(i.y-t.y);n.x=o.x+n.width/2;n.y=t.y+n.height/2}}));t.A(e.nodes(),(function(r){if(e.node(r).dummy==="border"){e.removeNode(r)}}))}function hi(e){t.A(e.edges(),(function(r){if(r.v===r.w){var n=e.node(r.v);if(!n.selfEdges){n.selfEdges=[]}n.selfEdges.push({e:r,label:e.edge(r)});e.removeEdge(r)}}))}function li(e){var r=Ae(e);t.A(r,(function(r){var n=0;t.A(r,(function(r,i){var o=e.node(r);o.order=i+n;t.A(o.selfEdges,(function(r){ce(e,"selfedge",{width:r.label.width,height:r.label.height,rank:o.rank,order:i+ ++n,e:r.e,label:r.label},"_se")}));delete o.selfEdges}))}))}function gi(e){t.A(e.nodes(),(function(r){var n=e.node(r);if(n.dummy==="selfedge"){var t=e.node(n.e.v);var i=t.x+t.width/2;var o=t.y;var a=n.x-i;var u=t.height/2;e.setEdge(n.e,n.label);e.removeNode(r);n.label.points=[{x:i+2*a/3,y:o-u},{x:i+5*a/6,y:o-u},{x:i+a,y:o},{x:i+5*a/6,y:o+u},{x:i+2*a/3,y:o+u}];n.label.x=n.x;n.label.y=n.y}}))}function pi(e,r){return ie($(e,r),Number)}function Ai(e){var r={};t.A(e,(function(e,n){r[n.toLowerCase()]=e}));return r}},65791:(e,r,n)=>{n.d(r,{T:()=>y});var t=n(33659);var i=n(58807);var o=n(37947);var a=n(97133);var u=n(74650);var s=n(69769);var d=n(89523);var f=n(62040);var c=n(55881);var v=n(19363);var h=n(10654);var l=(0,c.A)((function(e){return(0,v.A)((0,f.A)(e,1,h.A,true))}));const g=l;var p=n(44882);var A=n(65339);var w="\0";var b="\0";var m="";class y{constructor(e={}){this._isDirected=Object.prototype.hasOwnProperty.call(e,"directed")?e.directed:true;this._isMultigraph=Object.prototype.hasOwnProperty.call(e,"multigraph")?e.multigraph:false;this._isCompound=Object.prototype.hasOwnProperty.call(e,"compound")?e.compound:false;this._label=undefined;this._defaultNodeLabelFn=t.A(undefined);this._defaultEdgeLabelFn=t.A(undefined);this._nodes={};if(this._isCompound){this._parent={};this._children={};this._children[b]={}}this._in={};this._preds={};this._out={};this._sucs={};this._edgeObjs={};this._edgeLabels={}}isDirected(){return this._isDirected}isMultigraph(){return this._isMultigraph}isCompound(){return this._isCompound}setGraph(e){this._label=e;return this}graph(){return this._label}setDefaultNodeLabel(e){if(!i.A(e)){e=t.A(e)}this._defaultNodeLabelFn=e;return this}nodeCount(){return this._nodeCount}nodes(){return o.A(this._nodes)}sources(){var e=this;return a.A(this.nodes(),(function(r){return u.A(e._in[r])}))}sinks(){var e=this;return a.A(this.nodes(),(function(r){return u.A(e._out[r])}))}setNodes(e,r){var n=arguments;var t=this;s.A(e,(function(e){if(n.length>1){t.setNode(e,r)}else{t.setNode(e)}}));return this}setNode(e,r){if(Object.prototype.hasOwnProperty.call(this._nodes,e)){if(arguments.length>1){this._nodes[e]=r}return this}this._nodes[e]=arguments.length>1?r:this._defaultNodeLabelFn(e);if(this._isCompound){this._parent[e]=b;this._children[e]={};this._children[b][e]=true}this._in[e]={};this._preds[e]={};this._out[e]={};this._sucs[e]={};++this._nodeCount;return this}node(e){return this._nodes[e]}hasNode(e){return Object.prototype.hasOwnProperty.call(this._nodes,e)}removeNode(e){if(Object.prototype.hasOwnProperty.call(this._nodes,e)){var r=e=>this.removeEdge(this._edgeObjs[e]);delete this._nodes[e];if(this._isCompound){this._removeFromParentsChildList(e);delete this._parent[e];s.A(this.children(e),(e=>{this.setParent(e)}));delete this._children[e]}s.A(o.A(this._in[e]),r);delete this._in[e];delete this._preds[e];s.A(o.A(this._out[e]),r);delete this._out[e];delete this._sucs[e];--this._nodeCount}return this}setParent(e,r){if(!this._isCompound){throw new Error("Cannot set parent in a non-compound graph")}if(d.A(r)){r=b}else{r+="";for(var n=r;!d.A(n);n=this.parent(n)){if(n===e){throw new Error("Setting "+r+" as parent of "+e+" would create a cycle")}}this.setNode(r)}this.setNode(e);this._removeFromParentsChildList(e);this._parent[e]=r;this._children[r][e]=true;return this}_removeFromParentsChildList(e){delete this._children[this._parent[e]][e]}parent(e){if(this._isCompound){var r=this._parent[e];if(r!==b){return r}}}children(e){if(d.A(e)){e=b}if(this._isCompound){var r=this._children[e];if(r){return o.A(r)}}else if(e===b){return this.nodes()}else if(this.hasNode(e)){return[]}}predecessors(e){var r=this._preds[e];if(r){return o.A(r)}}successors(e){var r=this._sucs[e];if(r){return o.A(r)}}neighbors(e){var r=this.predecessors(e);if(r){return g(r,this.successors(e))}}isLeaf(e){var r;if(this.isDirected()){r=this.successors(e)}else{r=this.neighbors(e)}return r.length===0}filterNodes(e){var r=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});r.setGraph(this.graph());var n=this;s.A(this._nodes,(function(n,t){if(e(t)){r.setNode(t,n)}}));s.A(this._edgeObjs,(function(e){if(r.hasNode(e.v)&&r.hasNode(e.w)){r.setEdge(e,n.edge(e))}}));var t={};function i(e){var o=n.parent(e);if(o===undefined||r.hasNode(o)){t[e]=o;return o}else if(o in t){return t[o]}else{return i(o)}}if(this._isCompound){s.A(r.nodes(),(function(e){r.setParent(e,i(e))}))}return r}setDefaultEdgeLabel(e){if(!i.A(e)){e=t.A(e)}this._defaultEdgeLabelFn=e;return this}edgeCount(){return this._edgeCount}edges(){return p.A(this._edgeObjs)}setPath(e,r){var n=this;var t=arguments;A.A(e,(function(e,i){if(t.length>1){n.setEdge(e,i,r)}else{n.setEdge(e,i)}return i}));return this}setEdge(){var e,r,n,t;var i=false;var o=arguments[0];if(typeof o==="object"&&o!==null&&"v"in o){e=o.v;r=o.w;n=o.name;if(arguments.length===2){t=arguments[1];i=true}}else{e=o;r=arguments[1];n=arguments[3];if(arguments.length>2){t=arguments[2];i=true}}e=""+e;r=""+r;if(!d.A(n)){n=""+n}var a=O(this._isDirected,e,r,n);if(Object.prototype.hasOwnProperty.call(this._edgeLabels,a)){if(i){this._edgeLabels[a]=t}return this}if(!d.A(n)&&!this._isMultigraph){throw new Error("Cannot set a named edge when isMultigraph = false")}this.setNode(e);this.setNode(r);this._edgeLabels[a]=i?t:this._defaultEdgeLabelFn(e,r,n);var u=k(this._isDirected,e,r,n);e=u.v;r=u.w;Object.freeze(u);this._edgeObjs[a]=u;_(this._preds[r],e);_(this._sucs[e],r);this._in[r][a]=u;this._out[e][a]=u;this._edgeCount++;return this}edge(e,r,n){var t=arguments.length===1?N(this._isDirected,arguments[0]):O(this._isDirected,e,r,n);return this._edgeLabels[t]}hasEdge(e,r,n){var t=arguments.length===1?N(this._isDirected,arguments[0]):O(this._isDirected,e,r,n);return Object.prototype.hasOwnProperty.call(this._edgeLabels,t)}removeEdge(e,r,n){var t=arguments.length===1?N(this._isDirected,arguments[0]):O(this._isDirected,e,r,n);var i=this._edgeObjs[t];if(i){e=i.v;r=i.w;delete this._edgeLabels[t];delete this._edgeObjs[t];E(this._preds[r],e);E(this._sucs[e],r);delete this._in[r][t];delete this._out[e][t];this._edgeCount--}return this}inEdges(e,r){var n=this._in[e];if(n){var t=p.A(n);if(!r){return t}return a.A(t,(function(e){return e.v===r}))}}outEdges(e,r){var n=this._out[e];if(n){var t=p.A(n);if(!r){return t}return a.A(t,(function(e){return e.w===r}))}}nodeEdges(e,r){var n=this.inEdges(e,r);if(n){return n.concat(this.outEdges(e,r))}}}y.prototype._nodeCount=0;y.prototype._edgeCount=0;function _(e,r){if(e[r]){e[r]++}else{e[r]=1}}function E(e,r){if(! --e[r]){delete e[r]}}function O(e,r,n,t){var i=""+r;var o=""+n;if(!e&&i>o){var a=i;i=o;o=a}return i+m+o+m+(d.A(t)?w:t)}function k(e,r,n,t){var i=""+r;var o=""+n;if(!e&&i>o){var a=i;i=o;o=a}var u={v:i,w:o};if(t){u.name=t}return u}function N(e,r){return O(e,r.v,r.w,r.name)}},84416:(e,r,n)=>{n.d(r,{T:()=>t.T});var t=n(65791);const i="2.1.9-pre"},95852:(e,r,n)=>{n.d(r,{A:()=>o});var t=n(62579);function i(e,r,n){var i=-1,o=e.length;while(++i<o){var a=e[i],u=r(a);if(u!=null&&(s===undefined?u===u&&!(0,t.A)(u):n(u,s))){var s=u,d=a}}return d}const o=i},51135:(e,r,n)=>{n.d(r,{A:()=>i});function t(e,r){return e<r}const i=t},97457:(e,r,n)=>{n.d(r,{A:()=>a});var t=n(15912);var i=n(21585);function o(e,r){var n=-1,o=(0,i.A)(e)?Array(e.length):[];(0,t.A)(e,(function(e,t,i){o[++n]=r(e,t,i)}));return o}const a=o},44835:(e,r,n)=>{n.d(r,{A:()=>v});var t=n(22883);var i=n(16542);var o=n(65900);var a=n(78912);var u=n(85356);var s=n(43512);function d(e,r,n,t){if(!(0,u.A)(e)){return e}r=(0,o.A)(r,e);var d=-1,f=r.length,c=f-1,v=e;while(v!=null&&++d<f){var h=(0,s.A)(r[d]),l=n;if(h==="__proto__"||h==="constructor"||h==="prototype"){return e}if(d!=c){var g=v[h];l=t?t(g,h,v):undefined;if(l===undefined){l=(0,u.A)(g)?g:(0,a.A)(r[d+1])?[]:{}}}(0,i.A)(v,h,l);v=v[h]}return e}const f=d;function c(e,r,n){var i=-1,a=r.length,u={};while(++i<a){var s=r[i],d=(0,t.A)(e,s);if(n(d,s)){f(u,(0,o.A)(s,e),d)}}return u}const v=c},38693:(e,r,n)=>{n.d(r,{A:()=>f});var t=n(55881);var i=n(24461);var o=n(31943);var a=n(13839);var u=Object.prototype;var s=u.hasOwnProperty;var d=(0,t.A)((function(e,r){e=Object(e);var n=-1;var t=r.length;var d=t>2?r[2]:undefined;if(d&&(0,o.A)(r[0],r[1],d)){t=1}while(++n<t){var f=r[n];var c=(0,a.A)(f);var v=-1;var h=c.length;while(++v<h){var l=c[v];var g=e[l];if(g===undefined||(0,i.A)(g,u[l])&&!s.call(e,l)){e[l]=f[l]}}}return e}));const f=d},85075:(e,r,n)=>{n.d(r,{A:()=>l});var t=n(1121);var i=n(21585);var o=n(37947);function a(e){return function(r,n,a){var u=Object(r);if(!(0,i.A)(r)){var s=(0,t.A)(n,3);r=(0,o.A)(r);n=function(e){return s(u[e],e,u)}}var d=e(r,n,a);return d>-1?u[s?r[d]:d]:undefined}}const u=a;var s=n(97314);var d=n(29914);var f=Math.max;function c(e,r,n){var i=e==null?0:e.length;if(!i){return-1}var o=n==null?0:(0,d.A)(n);if(o<0){o=f(i+o,0)}return(0,s.A)(e,(0,t.A)(r,3),o)}const v=c;var h=u(v);const l=h},74033:(e,r,n)=>{n.d(r,{A:()=>o});var t=n(62040);function i(e){var r=e==null?0:e.length;return r?(0,t.A)(e,1):[]}const o=i},2850:(e,r,n)=>{n.d(r,{A:()=>d});var t=Object.prototype;var i=t.hasOwnProperty;function o(e,r){return e!=null&&i.call(e,r)}const a=o;var u=n(64491);function s(e,r){return e!=null&&(0,u.A)(e,r,a)}const d=s},86378:(e,r,n)=>{n.d(r,{A:()=>s});var t=n(64128);var i=n(39990);var o=n(53315);var a="[object String]";function u(e){return typeof e=="string"||!(0,i.A)(e)&&(0,o.A)(e)&&(0,t.A)(e)==a}const s=u},80359:(e,r,n)=>{n.d(r,{A:()=>i});function t(e){var r=e==null?0:e.length;return r?e[r-1]:undefined}const i=t},8937:(e,r,n)=>{n.d(r,{A:()=>s});var t=n(98519);var i=n(1121);var o=n(97457);var a=n(39990);function u(e,r){var n=(0,a.A)(e)?t.A:o.A;return n(e,(0,i.A)(r,3))}const s=u},963:(e,r,n)=>{n.d(r,{A:()=>u});var t=n(95852);var i=n(51135);var o=n(63077);function a(e){return e&&e.length?(0,t.A)(e,o.A,i.A):undefined}const u=a},52712:(e,r,n)=>{n.d(r,{A:()=>y});var t=/\s/;function i(e){var r=e.length;while(r--&&t.test(e.charAt(r))){}return r}const o=i;var a=/^\s+/;function u(e){return e?e.slice(0,o(e)+1).replace(a,""):e}const s=u;var d=n(85356);var f=n(62579);var c=0/0;var v=/^[-+]0x[0-9a-f]+$/i;var h=/^0b[01]+$/i;var l=/^0o[0-7]+$/i;var g=parseInt;function p(e){if(typeof e=="number"){return e}if((0,f.A)(e)){return c}if((0,d.A)(e)){var r=typeof e.valueOf=="function"?e.valueOf():e;e=(0,d.A)(r)?r+"":r}if(typeof e!="string"){return e===0?e:+e}e=s(e);var n=h.test(e);return n||l.test(e)?g(e.slice(2),n?2:8):v.test(e)?c:+e}const A=p;var w=1/0,b=17976931348623157e292;function m(e){if(!e){return e===0?e:0}e=A(e);if(e===w||e===-w){var r=e<0?-1:1;return r*b}return e===e?e:0}const y=m},29914:(e,r,n)=>{n.d(r,{A:()=>o});var t=n(52712);function i(e){var r=(0,t.A)(e),n=r%1;return r===r?n?r-n:r:0}const o=i}}]);