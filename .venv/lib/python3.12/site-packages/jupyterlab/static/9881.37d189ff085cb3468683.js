"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[9881],{79881:(t,i,e)=>{e.d(i,{diagram:()=>nt});var s=e(76261);var a=e(96049);var n=e(93113);var h=e(75905);var o=e(24982);var r=function(){var t=(0,h.K2)((function(t,i,e,s){for(e=e||{},s=t.length;s--;e[t[s]]=i);return e}),"o"),i=[1,10,12,14,16,18,19,21,23],e=[2,6],s=[1,3],a=[1,5],n=[1,6],o=[1,7],r=[1,5,10,12,14,16,18,19,21,23,34,35,36],l=[1,25],c=[1,26],u=[1,28],g=[1,29],x=[1,30],f=[1,31],p=[1,32],d=[1,33],y=[1,34],m=[1,35],b=[1,36],k=[1,37],A=[1,43],S=[1,42],w=[1,47],C=[1,50],_=[1,10,12,14,16,18,19,21,23,34,35,36],T=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36],R=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36,41,42,43,44,45,46,47,48,49,50],D=[1,64];var v={trace:(0,h.K2)((function t(){}),"trace"),yy:{},symbols_:{error:2,start:3,eol:4,XYCHART:5,chartConfig:6,document:7,CHART_ORIENTATION:8,statement:9,title:10,text:11,X_AXIS:12,parseXAxis:13,Y_AXIS:14,parseYAxis:15,LINE:16,plotData:17,BAR:18,acc_title:19,acc_title_value:20,acc_descr:21,acc_descr_value:22,acc_descr_multiline_value:23,SQUARE_BRACES_START:24,commaSeparatedNumbers:25,SQUARE_BRACES_END:26,NUMBER_WITH_DECIMAL:27,COMMA:28,xAxisData:29,bandData:30,ARROW_DELIMITER:31,commaSeparatedTexts:32,yAxisData:33,NEWLINE:34,SEMI:35,EOF:36,alphaNum:37,STR:38,MD_STR:39,alphaNumToken:40,AMP:41,NUM:42,ALPHA:43,PLUS:44,EQUALS:45,MULT:46,DOT:47,BRKT:48,MINUS:49,UNDERSCORE:50,$accept:0,$end:1},terminals_:{2:"error",5:"XYCHART",8:"CHART_ORIENTATION",10:"title",12:"X_AXIS",14:"Y_AXIS",16:"LINE",18:"BAR",19:"acc_title",20:"acc_title_value",21:"acc_descr",22:"acc_descr_value",23:"acc_descr_multiline_value",24:"SQUARE_BRACES_START",26:"SQUARE_BRACES_END",27:"NUMBER_WITH_DECIMAL",28:"COMMA",31:"ARROW_DELIMITER",34:"NEWLINE",35:"SEMI",36:"EOF",38:"STR",39:"MD_STR",41:"AMP",42:"NUM",43:"ALPHA",44:"PLUS",45:"EQUALS",46:"MULT",47:"DOT",48:"BRKT",49:"MINUS",50:"UNDERSCORE"},productions_:[0,[3,2],[3,3],[3,2],[3,1],[6,1],[7,0],[7,2],[9,2],[9,2],[9,2],[9,2],[9,2],[9,3],[9,2],[9,3],[9,2],[9,2],[9,1],[17,3],[25,3],[25,1],[13,1],[13,2],[13,1],[29,1],[29,3],[30,3],[32,3],[32,1],[15,1],[15,2],[15,1],[33,3],[4,1],[4,1],[4,1],[11,1],[11,1],[11,1],[37,1],[37,2],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1]],performAction:(0,h.K2)((function t(i,e,s,a,n,h,o){var r=h.length-1;switch(n){case 5:a.setOrientation(h[r]);break;case 9:a.setDiagramTitle(h[r].text.trim());break;case 12:a.setLineData({text:"",type:"text"},h[r]);break;case 13:a.setLineData(h[r-1],h[r]);break;case 14:a.setBarData({text:"",type:"text"},h[r]);break;case 15:a.setBarData(h[r-1],h[r]);break;case 16:this.$=h[r].trim();a.setAccTitle(this.$);break;case 17:case 18:this.$=h[r].trim();a.setAccDescription(this.$);break;case 19:this.$=h[r-1];break;case 20:this.$=[Number(h[r-2]),...h[r]];break;case 21:this.$=[Number(h[r])];break;case 22:a.setXAxisTitle(h[r]);break;case 23:a.setXAxisTitle(h[r-1]);break;case 24:a.setXAxisTitle({type:"text",text:""});break;case 25:a.setXAxisBand(h[r]);break;case 26:a.setXAxisRangeData(Number(h[r-2]),Number(h[r]));break;case 27:this.$=h[r-1];break;case 28:this.$=[h[r-2],...h[r]];break;case 29:this.$=[h[r]];break;case 30:a.setYAxisTitle(h[r]);break;case 31:a.setYAxisTitle(h[r-1]);break;case 32:a.setYAxisTitle({type:"text",text:""});break;case 33:a.setYAxisRangeData(Number(h[r-2]),Number(h[r]));break;case 37:this.$={text:h[r],type:"text"};break;case 38:this.$={text:h[r],type:"text"};break;case 39:this.$={text:h[r],type:"markdown"};break;case 40:this.$=h[r];break;case 41:this.$=h[r-1]+""+h[r];break}}),"anonymous"),table:[t(i,e,{3:1,4:2,7:4,5:s,34:a,35:n,36:o}),{1:[3]},t(i,e,{4:2,7:4,3:8,5:s,34:a,35:n,36:o}),t(i,e,{4:2,7:4,6:9,3:10,5:s,8:[1,11],34:a,35:n,36:o}),{1:[2,4],9:12,10:[1,13],12:[1,14],14:[1,15],16:[1,16],18:[1,17],19:[1,18],21:[1,19],23:[1,20]},t(r,[2,34]),t(r,[2,35]),t(r,[2,36]),{1:[2,1]},t(i,e,{4:2,7:4,3:21,5:s,34:a,35:n,36:o}),{1:[2,3]},t(r,[2,5]),t(i,[2,7],{4:22,34:a,35:n,36:o}),{11:23,37:24,38:l,39:c,40:27,41:u,42:g,43:x,44:f,45:p,46:d,47:y,48:m,49:b,50:k},{11:39,13:38,24:A,27:S,29:40,30:41,37:24,38:l,39:c,40:27,41:u,42:g,43:x,44:f,45:p,46:d,47:y,48:m,49:b,50:k},{11:45,15:44,27:w,33:46,37:24,38:l,39:c,40:27,41:u,42:g,43:x,44:f,45:p,46:d,47:y,48:m,49:b,50:k},{11:49,17:48,24:C,37:24,38:l,39:c,40:27,41:u,42:g,43:x,44:f,45:p,46:d,47:y,48:m,49:b,50:k},{11:52,17:51,24:C,37:24,38:l,39:c,40:27,41:u,42:g,43:x,44:f,45:p,46:d,47:y,48:m,49:b,50:k},{20:[1,53]},{22:[1,54]},t(_,[2,18]),{1:[2,2]},t(_,[2,8]),t(_,[2,9]),t(T,[2,37],{40:55,41:u,42:g,43:x,44:f,45:p,46:d,47:y,48:m,49:b,50:k}),t(T,[2,38]),t(T,[2,39]),t(R,[2,40]),t(R,[2,42]),t(R,[2,43]),t(R,[2,44]),t(R,[2,45]),t(R,[2,46]),t(R,[2,47]),t(R,[2,48]),t(R,[2,49]),t(R,[2,50]),t(R,[2,51]),t(_,[2,10]),t(_,[2,22],{30:41,29:56,24:A,27:S}),t(_,[2,24]),t(_,[2,25]),{31:[1,57]},{11:59,32:58,37:24,38:l,39:c,40:27,41:u,42:g,43:x,44:f,45:p,46:d,47:y,48:m,49:b,50:k},t(_,[2,11]),t(_,[2,30],{33:60,27:w}),t(_,[2,32]),{31:[1,61]},t(_,[2,12]),{17:62,24:C},{25:63,27:D},t(_,[2,14]),{17:65,24:C},t(_,[2,16]),t(_,[2,17]),t(R,[2,41]),t(_,[2,23]),{27:[1,66]},{26:[1,67]},{26:[2,29],28:[1,68]},t(_,[2,31]),{27:[1,69]},t(_,[2,13]),{26:[1,70]},{26:[2,21],28:[1,71]},t(_,[2,15]),t(_,[2,26]),t(_,[2,27]),{11:59,32:72,37:24,38:l,39:c,40:27,41:u,42:g,43:x,44:f,45:p,46:d,47:y,48:m,49:b,50:k},t(_,[2,33]),t(_,[2,19]),{25:73,27:D},{26:[2,28]},{26:[2,20]}],defaultActions:{8:[2,1],10:[2,3],21:[2,2],72:[2,28],73:[2,20]},parseError:(0,h.K2)((function t(i,e){if(e.recoverable){this.trace(i)}else{var s=new Error(i);s.hash=e;throw s}}),"parseError"),parse:(0,h.K2)((function t(i){var e=this,s=[0],a=[],n=[null],o=[],r=this.table,l="",c=0,u=0,g=0,x=2,f=1;var p=o.slice.call(arguments,1);var d=Object.create(this.lexer);var y={yy:{}};for(var m in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,m)){y.yy[m]=this.yy[m]}}d.setInput(i,y.yy);y.yy.lexer=d;y.yy.parser=this;if(typeof d.yylloc=="undefined"){d.yylloc={}}var b=d.yylloc;o.push(b);var k=d.options&&d.options.ranges;if(typeof y.yy.parseError==="function"){this.parseError=y.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function A(t){s.length=s.length-2*t;n.length=n.length-t;o.length=o.length-t}(0,h.K2)(A,"popStack");function S(){var t;t=a.pop()||d.lex()||f;if(typeof t!=="number"){if(t instanceof Array){a=t;t=a.pop()}t=e.symbols_[t]||t}return t}(0,h.K2)(S,"lex");var w,C,_,T,R,D,v={},L,P,E,K;while(true){_=s[s.length-1];if(this.defaultActions[_]){T=this.defaultActions[_]}else{if(w===null||typeof w=="undefined"){w=S()}T=r[_]&&r[_][w]}if(typeof T==="undefined"||!T.length||!T[0]){var I="";K=[];for(L in r[_]){if(this.terminals_[L]&&L>x){K.push("'"+this.terminals_[L]+"'")}}if(d.showPosition){I="Parse error on line "+(c+1)+":\n"+d.showPosition()+"\nExpecting "+K.join(", ")+", got '"+(this.terminals_[w]||w)+"'"}else{I="Parse error on line "+(c+1)+": Unexpected "+(w==f?"end of input":"'"+(this.terminals_[w]||w)+"'")}this.parseError(I,{text:d.match,token:this.terminals_[w]||w,line:d.yylineno,loc:b,expected:K})}if(T[0]instanceof Array&&T.length>1){throw new Error("Parse Error: multiple actions possible at state: "+_+", token: "+w)}switch(T[0]){case 1:s.push(w);n.push(d.yytext);o.push(d.yylloc);s.push(T[1]);w=null;if(!C){u=d.yyleng;l=d.yytext;c=d.yylineno;b=d.yylloc;if(g>0){g--}}else{w=C;C=null}break;case 2:P=this.productions_[T[1]][1];v.$=n[n.length-P];v._$={first_line:o[o.length-(P||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(P||1)].first_column,last_column:o[o.length-1].last_column};if(k){v._$.range=[o[o.length-(P||1)].range[0],o[o.length-1].range[1]]}D=this.performAction.apply(v,[l,u,c,y.yy,T[1],n,o].concat(p));if(typeof D!=="undefined"){return D}if(P){s=s.slice(0,-1*P*2);n=n.slice(0,-1*P);o=o.slice(0,-1*P)}s.push(this.productions_[T[1]][0]);n.push(v.$);o.push(v._$);E=r[s[s.length-2]][s[s.length-1]];s.push(E);break;case 3:return true}}return true}),"parse")};var L=function(){var t={EOF:1,parseError:(0,h.K2)((function t(i,e){if(this.yy.parser){this.yy.parser.parseError(i,e)}else{throw new Error(i)}}),"parseError"),setInput:(0,h.K2)((function(t,i){this.yy=i||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,h.K2)((function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var i=t.match(/(?:\r\n?|\n).*/g);if(i){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t}),"input"),unput:(0,h.K2)((function(t){var i=t.length;var e=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-i);this.offset-=i;var s=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(e.length-1){this.yylineno-=e.length-1}var a=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:e?(e.length===s.length?this.yylloc.first_column:0)+s[s.length-e.length].length-e[0].length:this.yylloc.first_column-i};if(this.options.ranges){this.yylloc.range=[a[0],a[0]+this.yyleng-i]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,h.K2)((function(){this._more=true;return this}),"more"),reject:(0,h.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,h.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,h.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,h.K2)((function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,h.K2)((function(){var t=this.pastInput();var i=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+i+"^"}),"showPosition"),test_match:(0,h.K2)((function(t,i){var e,s,a;if(this.options.backtrack_lexer){a={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){a.yylloc.range=this.yylloc.range.slice(0)}}s=t[0].match(/(?:\r\n?|\n).*/g);if(s){this.yylineno+=s.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:s?s[s.length-1].length-s[s.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];e=this.performAction.call(this,this.yy,this,i,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(e){return e}else if(this._backtrack){for(var n in a){this[n]=a[n]}return false}return false}),"test_match"),next:(0,h.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,i,e,s;if(!this._more){this.yytext="";this.match=""}var a=this._currentRules();for(var n=0;n<a.length;n++){e=this._input.match(this.rules[a[n]]);if(e&&(!i||e[0].length>i[0].length)){i=e;s=n;if(this.options.backtrack_lexer){t=this.test_match(e,a[n]);if(t!==false){return t}else if(this._backtrack){i=false;continue}else{return false}}else if(!this.options.flex){break}}}if(i){t=this.test_match(i,a[s]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,h.K2)((function t(){var i=this.next();if(i){return i}else{return this.lex()}}),"lex"),begin:(0,h.K2)((function t(i){this.conditionStack.push(i)}),"begin"),popState:(0,h.K2)((function t(){var i=this.conditionStack.length-1;if(i>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,h.K2)((function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,h.K2)((function t(i){i=this.conditionStack.length-1-Math.abs(i||0);if(i>=0){return this.conditionStack[i]}else{return"INITIAL"}}),"topState"),pushState:(0,h.K2)((function t(i){this.begin(i)}),"pushState"),stateStackSize:(0,h.K2)((function t(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":true},performAction:(0,h.K2)((function t(i,e,s,a){var n=a;switch(s){case 0:break;case 1:break;case 2:this.popState();return 34;break;case 3:this.popState();return 34;break;case 4:return 34;break;case 5:break;case 6:return 10;break;case 7:this.pushState("acc_title");return 19;break;case 8:this.popState();return"acc_title_value";break;case 9:this.pushState("acc_descr");return 21;break;case 10:this.popState();return"acc_descr_value";break;case 11:this.pushState("acc_descr_multiline");break;case 12:this.popState();break;case 13:return"acc_descr_multiline_value";break;case 14:return 5;break;case 15:return 8;break;case 16:this.pushState("axis_data");return"X_AXIS";break;case 17:this.pushState("axis_data");return"Y_AXIS";break;case 18:this.pushState("axis_band_data");return 24;break;case 19:return 31;break;case 20:this.pushState("data");return 16;break;case 21:this.pushState("data");return 18;break;case 22:this.pushState("data_inner");return 24;break;case 23:return 27;break;case 24:this.popState();return 26;break;case 25:this.popState();break;case 26:this.pushState("string");break;case 27:this.popState();break;case 28:return"STR";break;case 29:return 24;break;case 30:return 26;break;case 31:return 43;break;case 32:return"COLON";break;case 33:return 44;break;case 34:return 28;break;case 35:return 45;break;case 36:return 46;break;case 37:return 48;break;case 38:return 50;break;case 39:return 47;break;case 40:return 41;break;case 41:return 49;break;case 42:return 42;break;case 43:break;case 44:return 35;break;case 45:return 36;break}}),"anonymous"),rules:[/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:(\r?\n))/i,/^(?:(\r?\n))/i,/^(?:[\n\r]+)/i,/^(?:%%[^\n]*)/i,/^(?:title\b)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:\{)/i,/^(?:[^\}]*)/i,/^(?:xychart-beta\b)/i,/^(?:(?:vertical|horizontal))/i,/^(?:x-axis\b)/i,/^(?:y-axis\b)/i,/^(?:\[)/i,/^(?:-->)/i,/^(?:line\b)/i,/^(?:bar\b)/i,/^(?:\[)/i,/^(?:[+-]?(?:\d+(?:\.\d+)?|\.\d+))/i,/^(?:\])/i,/^(?:(?:`\)                                    \{ this\.pushState\(md_string\); \}\n<md_string>\(\?:\(\?!`"\)\.\)\+                  \{ return MD_STR; \}\n<md_string>\(\?:`))/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:\[)/i,/^(?:\])/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:\*)/i,/^(?:#)/i,/^(?:[\_])/i,/^(?:\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\s+)/i,/^(?:;)/i,/^(?:$)/i],conditions:{data_inner:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,23,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:true},data:{rules:[0,1,3,4,5,6,7,9,11,14,15,16,17,20,21,22,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:true},axis_band_data:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:true},axis_data:{rules:[0,1,2,4,5,6,7,9,11,14,15,16,17,18,19,20,21,23,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:true},acc_descr_multiline:{rules:[12,13],inclusive:false},acc_descr:{rules:[10],inclusive:false},acc_title:{rules:[8],inclusive:false},title:{rules:[],inclusive:false},md_string:{rules:[],inclusive:false},string:{rules:[27,28],inclusive:false},INITIAL:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:true}}};return t}();v.lexer=L;function P(){this.yy={}}(0,h.K2)(P,"Parser");P.prototype=v;v.Parser=P;return new P}();r.parser=r;var l=r;function c(t){return t.type==="bar"}(0,h.K2)(c,"isBarPlot");function u(t){return t.type==="band"}(0,h.K2)(u,"isBandAxisData");function g(t){return t.type==="linear"}(0,h.K2)(g,"isLinearAxisData");var x=class{constructor(t){this.parentGroup=t}static{(0,h.K2)(this,"TextDimensionCalculatorWithFont")}getMaxDimension(t,i){if(!this.parentGroup){return{width:t.reduce(((t,i)=>Math.max(i.length,t)),0)*i,height:i}}const e={width:0,height:0};const a=this.parentGroup.append("g").attr("visibility","hidden").attr("font-size",i);for(const n of t){const t=(0,s.W6)(a,1,n);const h=t?t.width:n.length*i;const o=t?t.height:i;e.width=Math.max(e.width,h);e.height=Math.max(e.height,o)}a.remove();return e}};var f=.7;var p=.2;var d=class{constructor(t,i,e,s){this.axisConfig=t;this.title=i;this.textDimensionCalculator=e;this.axisThemeConfig=s;this.boundingRect={x:0,y:0,width:0,height:0};this.axisPosition="left";this.showTitle=false;this.showLabel=false;this.showTick=false;this.showAxisLine=false;this.outerPadding=0;this.titleTextHeight=0;this.labelTextHeight=0;this.range=[0,10];this.boundingRect={x:0,y:0,width:0,height:0};this.axisPosition="left"}static{(0,h.K2)(this,"BaseAxis")}setRange(t){this.range=t;if(this.axisPosition==="left"||this.axisPosition==="right"){this.boundingRect.height=t[1]-t[0]}else{this.boundingRect.width=t[1]-t[0]}this.recalculateScale()}getRange(){return[this.range[0]+this.outerPadding,this.range[1]-this.outerPadding]}setAxisPosition(t){this.axisPosition=t;this.setRange(this.range)}getTickDistance(){const t=this.getRange();return Math.abs(t[0]-t[1])/this.getTickValues().length}getAxisOuterPadding(){return this.outerPadding}getLabelDimension(){return this.textDimensionCalculator.getMaxDimension(this.getTickValues().map((t=>t.toString())),this.axisConfig.labelFontSize)}recalculateOuterPaddingToDrawBar(){if(f*this.getTickDistance()>this.outerPadding*2){this.outerPadding=Math.floor(f*this.getTickDistance()/2)}this.recalculateScale()}calculateSpaceIfDrawnHorizontally(t){let i=t.height;if(this.axisConfig.showAxisLine&&i>this.axisConfig.axisLineWidth){i-=this.axisConfig.axisLineWidth;this.showAxisLine=true}if(this.axisConfig.showLabel){const e=this.getLabelDimension();const s=p*t.width;this.outerPadding=Math.min(e.width/2,s);const a=e.height+this.axisConfig.labelPadding*2;this.labelTextHeight=e.height;if(a<=i){i-=a;this.showLabel=true}}if(this.axisConfig.showTick&&i>=this.axisConfig.tickLength){this.showTick=true;i-=this.axisConfig.tickLength}if(this.axisConfig.showTitle&&this.title){const t=this.textDimensionCalculator.getMaxDimension([this.title],this.axisConfig.titleFontSize);const e=t.height+this.axisConfig.titlePadding*2;this.titleTextHeight=t.height;if(e<=i){i-=e;this.showTitle=true}}this.boundingRect.width=t.width;this.boundingRect.height=t.height-i}calculateSpaceIfDrawnVertical(t){let i=t.width;if(this.axisConfig.showAxisLine&&i>this.axisConfig.axisLineWidth){i-=this.axisConfig.axisLineWidth;this.showAxisLine=true}if(this.axisConfig.showLabel){const e=this.getLabelDimension();const s=p*t.height;this.outerPadding=Math.min(e.height/2,s);const a=e.width+this.axisConfig.labelPadding*2;if(a<=i){i-=a;this.showLabel=true}}if(this.axisConfig.showTick&&i>=this.axisConfig.tickLength){this.showTick=true;i-=this.axisConfig.tickLength}if(this.axisConfig.showTitle&&this.title){const t=this.textDimensionCalculator.getMaxDimension([this.title],this.axisConfig.titleFontSize);const e=t.height+this.axisConfig.titlePadding*2;this.titleTextHeight=t.height;if(e<=i){i-=e;this.showTitle=true}}this.boundingRect.width=t.width-i;this.boundingRect.height=t.height}calculateSpace(t){if(this.axisPosition==="left"||this.axisPosition==="right"){this.calculateSpaceIfDrawnVertical(t)}else{this.calculateSpaceIfDrawnHorizontally(t)}this.recalculateScale();return{width:this.boundingRect.width,height:this.boundingRect.height}}setBoundingBoxXY(t){this.boundingRect.x=t.x;this.boundingRect.y=t.y}getDrawableElementsForLeftAxis(){const t=[];if(this.showAxisLine){const i=this.boundingRect.x+this.boundingRect.width-this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["left-axis","axisl-line"],data:[{path:`M ${i},${this.boundingRect.y} L ${i},${this.boundingRect.y+this.boundingRect.height} `,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel){t.push({type:"text",groupTexts:["left-axis","label"],data:this.getTickValues().map((t=>({text:t.toString(),x:this.boundingRect.x+this.boundingRect.width-(this.showLabel?this.axisConfig.labelPadding:0)-(this.showTick?this.axisConfig.tickLength:0)-(this.showAxisLine?this.axisConfig.axisLineWidth:0),y:this.getScaleValue(t),fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"middle",horizontalPos:"right"})))})}if(this.showTick){const i=this.boundingRect.x+this.boundingRect.width-(this.showAxisLine?this.axisConfig.axisLineWidth:0);t.push({type:"path",groupTexts:["left-axis","ticks"],data:this.getTickValues().map((t=>({path:`M ${i},${this.getScaleValue(t)} L ${i-this.axisConfig.tickLength},${this.getScaleValue(t)}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth})))})}if(this.showTitle){t.push({type:"text",groupTexts:["left-axis","title"],data:[{text:this.title,x:this.boundingRect.x+this.axisConfig.titlePadding,y:this.boundingRect.y+this.boundingRect.height/2,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:270,verticalPos:"top",horizontalPos:"center"}]})}return t}getDrawableElementsForBottomAxis(){const t=[];if(this.showAxisLine){const i=this.boundingRect.y+this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["bottom-axis","axis-line"],data:[{path:`M ${this.boundingRect.x},${i} L ${this.boundingRect.x+this.boundingRect.width},${i}`,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel){t.push({type:"text",groupTexts:["bottom-axis","label"],data:this.getTickValues().map((t=>({text:t.toString(),x:this.getScaleValue(t),y:this.boundingRect.y+this.axisConfig.labelPadding+(this.showTick?this.axisConfig.tickLength:0)+(this.showAxisLine?this.axisConfig.axisLineWidth:0),fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"})))})}if(this.showTick){const i=this.boundingRect.y+(this.showAxisLine?this.axisConfig.axisLineWidth:0);t.push({type:"path",groupTexts:["bottom-axis","ticks"],data:this.getTickValues().map((t=>({path:`M ${this.getScaleValue(t)},${i} L ${this.getScaleValue(t)},${i+this.axisConfig.tickLength}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth})))})}if(this.showTitle){t.push({type:"text",groupTexts:["bottom-axis","title"],data:[{text:this.title,x:this.range[0]+(this.range[1]-this.range[0])/2,y:this.boundingRect.y+this.boundingRect.height-this.axisConfig.titlePadding-this.titleTextHeight,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}]})}return t}getDrawableElementsForTopAxis(){const t=[];if(this.showAxisLine){const i=this.boundingRect.y+this.boundingRect.height-this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["top-axis","axis-line"],data:[{path:`M ${this.boundingRect.x},${i} L ${this.boundingRect.x+this.boundingRect.width},${i}`,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel){t.push({type:"text",groupTexts:["top-axis","label"],data:this.getTickValues().map((t=>({text:t.toString(),x:this.getScaleValue(t),y:this.boundingRect.y+(this.showTitle?this.titleTextHeight+this.axisConfig.titlePadding*2:0)+this.axisConfig.labelPadding,fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"})))})}if(this.showTick){const i=this.boundingRect.y;t.push({type:"path",groupTexts:["top-axis","ticks"],data:this.getTickValues().map((t=>({path:`M ${this.getScaleValue(t)},${i+this.boundingRect.height-(this.showAxisLine?this.axisConfig.axisLineWidth:0)} L ${this.getScaleValue(t)},${i+this.boundingRect.height-this.axisConfig.tickLength-(this.showAxisLine?this.axisConfig.axisLineWidth:0)}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth})))})}if(this.showTitle){t.push({type:"text",groupTexts:["top-axis","title"],data:[{text:this.title,x:this.boundingRect.x+this.boundingRect.width/2,y:this.boundingRect.y+this.axisConfig.titlePadding,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}]})}return t}getDrawableElements(){if(this.axisPosition==="left"){return this.getDrawableElementsForLeftAxis()}if(this.axisPosition==="right"){throw Error("Drawing of right axis is not implemented")}if(this.axisPosition==="bottom"){return this.getDrawableElementsForBottomAxis()}if(this.axisPosition==="top"){return this.getDrawableElementsForTopAxis()}return[]}};var y=class extends d{static{(0,h.K2)(this,"BandAxis")}constructor(t,i,e,s,a){super(t,s,a,i);this.categories=e;this.scale=(0,o.WH)().domain(this.categories).range(this.getRange())}setRange(t){super.setRange(t)}recalculateScale(){this.scale=(0,o.WH)().domain(this.categories).range(this.getRange()).paddingInner(1).paddingOuter(0).align(.5);h.Rm.trace("BandAxis axis final categories, range: ",this.categories,this.getRange())}getTickValues(){return this.categories}getScaleValue(t){return this.scale(t)??this.getRange()[0]}};var m=class extends d{static{(0,h.K2)(this,"LinearAxis")}constructor(t,i,e,s,a){super(t,s,a,i);this.domain=e;this.scale=(0,o.m4Y)().domain(this.domain).range(this.getRange())}getTickValues(){return this.scale.ticks()}recalculateScale(){const t=[...this.domain];if(this.axisPosition==="left"){t.reverse()}this.scale=(0,o.m4Y)().domain(t).range(this.getRange())}getScaleValue(t){return this.scale(t)}};function b(t,i,e,s){const a=new x(s);if(u(t)){return new y(i,e,t.categories,t.title,a)}return new m(i,e,[t.min,t.max],t.title,a)}(0,h.K2)(b,"getAxis");var k=class{constructor(t,i,e,s){this.textDimensionCalculator=t;this.chartConfig=i;this.chartData=e;this.chartThemeConfig=s;this.boundingRect={x:0,y:0,width:0,height:0};this.showChartTitle=false}static{(0,h.K2)(this,"ChartTitle")}setBoundingBoxXY(t){this.boundingRect.x=t.x;this.boundingRect.y=t.y}calculateSpace(t){const i=this.textDimensionCalculator.getMaxDimension([this.chartData.title],this.chartConfig.titleFontSize);const e=Math.max(i.width,t.width);const s=i.height+2*this.chartConfig.titlePadding;if(i.width<=e&&i.height<=s&&this.chartConfig.showTitle&&this.chartData.title){this.boundingRect.width=e;this.boundingRect.height=s;this.showChartTitle=true}return{width:this.boundingRect.width,height:this.boundingRect.height}}getDrawableElements(){const t=[];if(this.showChartTitle){t.push({groupTexts:["chart-title"],type:"text",data:[{fontSize:this.chartConfig.titleFontSize,text:this.chartData.title,verticalPos:"middle",horizontalPos:"center",x:this.boundingRect.x+this.boundingRect.width/2,y:this.boundingRect.y+this.boundingRect.height/2,fill:this.chartThemeConfig.titleColor,rotation:0}]})}return t}};function A(t,i,e,s){const a=new x(s);return new k(a,t,i,e)}(0,h.K2)(A,"getChartTitleComponent");var S=class{constructor(t,i,e,s,a){this.plotData=t;this.xAxis=i;this.yAxis=e;this.orientation=s;this.plotIndex=a}static{(0,h.K2)(this,"LinePlot")}getDrawableElement(){const t=this.plotData.data.map((t=>[this.xAxis.getScaleValue(t[0]),this.yAxis.getScaleValue(t[1])]));let i;if(this.orientation==="horizontal"){i=(0,o.n8j)().y((t=>t[0])).x((t=>t[1]))(t)}else{i=(0,o.n8j)().x((t=>t[0])).y((t=>t[1]))(t)}if(!i){return[]}return[{groupTexts:["plot",`line-plot-${this.plotIndex}`],type:"path",data:[{path:i,strokeFill:this.plotData.strokeFill,strokeWidth:this.plotData.strokeWidth}]}]}};var w=class{constructor(t,i,e,s,a,n){this.barData=t;this.boundingRect=i;this.xAxis=e;this.yAxis=s;this.orientation=a;this.plotIndex=n}static{(0,h.K2)(this,"BarPlot")}getDrawableElement(){const t=this.barData.data.map((t=>[this.xAxis.getScaleValue(t[0]),this.yAxis.getScaleValue(t[1])]));const i=.05;const e=Math.min(this.xAxis.getAxisOuterPadding()*2,this.xAxis.getTickDistance())*(1-i);const s=e/2;if(this.orientation==="horizontal"){return[{groupTexts:["plot",`bar-plot-${this.plotIndex}`],type:"rect",data:t.map((t=>({x:this.boundingRect.x,y:t[0]-s,height:e,width:t[1]-this.boundingRect.x,fill:this.barData.fill,strokeWidth:0,strokeFill:this.barData.fill})))}]}return[{groupTexts:["plot",`bar-plot-${this.plotIndex}`],type:"rect",data:t.map((t=>({x:t[0]-s,y:t[1],width:e,height:this.boundingRect.y+this.boundingRect.height-t[1],fill:this.barData.fill,strokeWidth:0,strokeFill:this.barData.fill})))}]}};var C=class{constructor(t,i,e){this.chartConfig=t;this.chartData=i;this.chartThemeConfig=e;this.boundingRect={x:0,y:0,width:0,height:0}}static{(0,h.K2)(this,"BasePlot")}setAxes(t,i){this.xAxis=t;this.yAxis=i}setBoundingBoxXY(t){this.boundingRect.x=t.x;this.boundingRect.y=t.y}calculateSpace(t){this.boundingRect.width=t.width;this.boundingRect.height=t.height;return{width:this.boundingRect.width,height:this.boundingRect.height}}getDrawableElements(){if(!(this.xAxis&&this.yAxis)){throw Error("Axes must be passed to render Plots")}const t=[];for(const[i,e]of this.chartData.plots.entries()){switch(e.type){case"line":{const s=new S(e,this.xAxis,this.yAxis,this.chartConfig.chartOrientation,i);t.push(...s.getDrawableElement())}break;case"bar":{const s=new w(e,this.boundingRect,this.xAxis,this.yAxis,this.chartConfig.chartOrientation,i);t.push(...s.getDrawableElement())}break}}return t}};function _(t,i,e){return new C(t,i,e)}(0,h.K2)(_,"getPlotComponent");var T=class{constructor(t,i,e,s){this.chartConfig=t;this.chartData=i;this.componentStore={title:A(t,i,e,s),plot:_(t,i,e),xAxis:b(i.xAxis,t.xAxis,{titleColor:e.xAxisTitleColor,labelColor:e.xAxisLabelColor,tickColor:e.xAxisTickColor,axisLineColor:e.xAxisLineColor},s),yAxis:b(i.yAxis,t.yAxis,{titleColor:e.yAxisTitleColor,labelColor:e.yAxisLabelColor,tickColor:e.yAxisTickColor,axisLineColor:e.yAxisLineColor},s)}}static{(0,h.K2)(this,"Orchestrator")}calculateVerticalSpace(){let t=this.chartConfig.width;let i=this.chartConfig.height;let e=0;let s=0;let a=Math.floor(t*this.chartConfig.plotReservedSpacePercent/100);let n=Math.floor(i*this.chartConfig.plotReservedSpacePercent/100);let h=this.componentStore.plot.calculateSpace({width:a,height:n});t-=h.width;i-=h.height;h=this.componentStore.title.calculateSpace({width:this.chartConfig.width,height:i});s=h.height;i-=h.height;this.componentStore.xAxis.setAxisPosition("bottom");h=this.componentStore.xAxis.calculateSpace({width:t,height:i});i-=h.height;this.componentStore.yAxis.setAxisPosition("left");h=this.componentStore.yAxis.calculateSpace({width:t,height:i});e=h.width;t-=h.width;if(t>0){a+=t;t=0}if(i>0){n+=i;i=0}this.componentStore.plot.calculateSpace({width:a,height:n});this.componentStore.plot.setBoundingBoxXY({x:e,y:s});this.componentStore.xAxis.setRange([e,e+a]);this.componentStore.xAxis.setBoundingBoxXY({x:e,y:s+n});this.componentStore.yAxis.setRange([s,s+n]);this.componentStore.yAxis.setBoundingBoxXY({x:0,y:s});if(this.chartData.plots.some((t=>c(t)))){this.componentStore.xAxis.recalculateOuterPaddingToDrawBar()}}calculateHorizontalSpace(){let t=this.chartConfig.width;let i=this.chartConfig.height;let e=0;let s=0;let a=0;let n=Math.floor(t*this.chartConfig.plotReservedSpacePercent/100);let h=Math.floor(i*this.chartConfig.plotReservedSpacePercent/100);let o=this.componentStore.plot.calculateSpace({width:n,height:h});t-=o.width;i-=o.height;o=this.componentStore.title.calculateSpace({width:this.chartConfig.width,height:i});e=o.height;i-=o.height;this.componentStore.xAxis.setAxisPosition("left");o=this.componentStore.xAxis.calculateSpace({width:t,height:i});t-=o.width;s=o.width;this.componentStore.yAxis.setAxisPosition("top");o=this.componentStore.yAxis.calculateSpace({width:t,height:i});i-=o.height;a=e+o.height;if(t>0){n+=t;t=0}if(i>0){h+=i;i=0}this.componentStore.plot.calculateSpace({width:n,height:h});this.componentStore.plot.setBoundingBoxXY({x:s,y:a});this.componentStore.yAxis.setRange([s,s+n]);this.componentStore.yAxis.setBoundingBoxXY({x:s,y:e});this.componentStore.xAxis.setRange([a,a+h]);this.componentStore.xAxis.setBoundingBoxXY({x:0,y:a});if(this.chartData.plots.some((t=>c(t)))){this.componentStore.xAxis.recalculateOuterPaddingToDrawBar()}}calculateSpace(){if(this.chartConfig.chartOrientation==="horizontal"){this.calculateHorizontalSpace()}else{this.calculateVerticalSpace()}}getDrawableElement(){this.calculateSpace();const t=[];this.componentStore.plot.setAxes(this.componentStore.xAxis,this.componentStore.yAxis);for(const i of Object.values(this.componentStore)){t.push(...i.getDrawableElements())}return t}};var R=class{static{(0,h.K2)(this,"XYChartBuilder")}static build(t,i,e,s){const a=new T(t,i,e,s);return a.getDrawableElement()}};var D=0;var v;var L=B();var P=M();var E=z();var K=P.plotColorPalette.split(",").map((t=>t.trim()));var I=false;var $=false;function M(){const t=(0,h.P$)();const i=(0,h.zj)();return(0,a.$t)(t.xyChart,i.themeVariables.xyChart)}(0,h.K2)(M,"getChartDefaultThemeConfig");function B(){const t=(0,h.zj)();return(0,a.$t)(h.UI.xyChart,t.xyChart)}(0,h.K2)(B,"getChartDefaultConfig");function z(){return{yAxis:{type:"linear",title:"",min:Infinity,max:-Infinity},xAxis:{type:"band",title:"",categories:[]},title:"",plots:[]}}(0,h.K2)(z,"getChartDefaultData");function W(t){const i=(0,h.zj)();return(0,h.jZ)(t.trim(),i)}(0,h.K2)(W,"textSanitizer");function O(t){v=t}(0,h.K2)(O,"setTmpSVGG");function F(t){if(t==="horizontal"){L.chartOrientation="horizontal"}else{L.chartOrientation="vertical"}}(0,h.K2)(F,"setOrientation");function N(t){E.xAxis.title=W(t.text)}(0,h.K2)(N,"setXAxisTitle");function V(t,i){E.xAxis={type:"linear",title:E.xAxis.title,min:t,max:i};I=true}(0,h.K2)(V,"setXAxisRangeData");function X(t){E.xAxis={type:"band",title:E.xAxis.title,categories:t.map((t=>W(t.text)))};I=true}(0,h.K2)(X,"setXAxisBand");function Y(t){E.yAxis.title=W(t.text)}(0,h.K2)(Y,"setYAxisTitle");function U(t,i){E.yAxis={type:"linear",title:E.yAxis.title,min:t,max:i};$=true}(0,h.K2)(U,"setYAxisRangeData");function H(t){const i=Math.min(...t);const e=Math.max(...t);const s=g(E.yAxis)?E.yAxis.min:Infinity;const a=g(E.yAxis)?E.yAxis.max:-Infinity;E.yAxis={type:"linear",title:E.yAxis.title,min:Math.min(s,i),max:Math.max(a,e)}}(0,h.K2)(H,"setYAxisRangeFromPlotData");function j(t){let i=[];if(t.length===0){return i}if(!I){const i=g(E.xAxis)?E.xAxis.min:Infinity;const e=g(E.xAxis)?E.xAxis.max:-Infinity;V(Math.min(i,1),Math.max(e,t.length))}if(!$){H(t)}if(u(E.xAxis)){i=E.xAxis.categories.map(((i,e)=>[i,t[e]]))}if(g(E.xAxis)){const e=E.xAxis.min;const s=E.xAxis.max;const a=(s-e)/(t.length-1);const n=[];for(let t=e;t<=s;t+=a){n.push(`${t}`)}i=n.map(((i,e)=>[i,t[e]]))}return i}(0,h.K2)(j,"transformDataWithoutCategory");function G(t){return K[t===0?0:t%K.length]}(0,h.K2)(G,"getPlotColorFromPalette");function Q(t,i){const e=j(i);E.plots.push({type:"line",strokeFill:G(D),strokeWidth:2,data:e});D++}(0,h.K2)(Q,"setLineData");function Z(t,i){const e=j(i);E.plots.push({type:"bar",fill:G(D),data:e});D++}(0,h.K2)(Z,"setBarData");function q(){if(E.plots.length===0){throw Error("No Plot to render, please provide a plot with some data")}E.title=(0,h.ab)();return R.build(L,E,P,v)}(0,h.K2)(q,"getDrawableElem");function J(){return P}(0,h.K2)(J,"getChartThemeConfig");function tt(){return L}(0,h.K2)(tt,"getChartConfig");var it=(0,h.K2)((function(){(0,h.IU)();D=0;L=B();E=z();P=M();K=P.plotColorPalette.split(",").map((t=>t.trim()));I=false;$=false}),"clear");var et={getDrawableElem:q,clear:it,setAccTitle:h.SV,getAccTitle:h.iN,setDiagramTitle:h.ke,getDiagramTitle:h.ab,getAccDescription:h.m7,setAccDescription:h.EI,setOrientation:F,setXAxisTitle:N,setXAxisRangeData:V,setXAxisBand:X,setYAxisTitle:Y,setYAxisRangeData:U,setLineData:Q,setBarData:Z,setTmpSVGG:O,getChartThemeConfig:J,getChartConfig:tt};var st=(0,h.K2)(((t,i,e,s)=>{const a=s.db;const o=a.getChartThemeConfig();const r=a.getChartConfig();function l(t){return t==="top"?"text-before-edge":"middle"}(0,h.K2)(l,"getDominantBaseLine");function c(t){return t==="left"?"start":t==="right"?"end":"middle"}(0,h.K2)(c,"getTextAnchor");function u(t){return`translate(${t.x}, ${t.y}) rotate(${t.rotation||0})`}(0,h.K2)(u,"getTextTransformation");h.Rm.debug("Rendering xychart chart\n"+t);const g=(0,n.D)(i);const x=g.append("g").attr("class","main");const f=x.append("rect").attr("width",r.width).attr("height",r.height).attr("class","background");(0,h.a$)(g,r.height,r.width,true);g.attr("viewBox",`0 0 ${r.width} ${r.height}`);f.attr("fill",o.backgroundColor);a.setTmpSVGG(g.append("g").attr("class","mermaid-tmp-group"));const p=a.getDrawableElem();const d={};function y(t){let i=x;let e="";for(const[s]of t.entries()){let a=x;if(s>0&&d[e]){a=d[e]}e+=t[s];i=d[e];if(!i){i=d[e]=a.append("g").attr("class",t[s])}}return i}(0,h.K2)(y,"getGroup");for(const n of p){if(n.data.length===0){continue}const t=y(n.groupTexts);switch(n.type){case"rect":t.selectAll("rect").data(n.data).enter().append("rect").attr("x",(t=>t.x)).attr("y",(t=>t.y)).attr("width",(t=>t.width)).attr("height",(t=>t.height)).attr("fill",(t=>t.fill)).attr("stroke",(t=>t.strokeFill)).attr("stroke-width",(t=>t.strokeWidth));break;case"text":t.selectAll("text").data(n.data).enter().append("text").attr("x",0).attr("y",0).attr("fill",(t=>t.fill)).attr("font-size",(t=>t.fontSize)).attr("dominant-baseline",(t=>l(t.verticalPos))).attr("text-anchor",(t=>c(t.horizontalPos))).attr("transform",(t=>u(t))).text((t=>t.text));break;case"path":t.selectAll("path").data(n.data).enter().append("path").attr("d",(t=>t.path)).attr("fill",(t=>t.fill?t.fill:"none")).attr("stroke",(t=>t.strokeFill)).attr("stroke-width",(t=>t.strokeWidth));break}}}),"draw");var at={draw:st};var nt={parser:l,db:et,renderer:at}}}]);