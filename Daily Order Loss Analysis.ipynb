import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)

print("📊 Daily Order Loss Analysis - Commercial Team Report")
print("=" * 60)

# Load the activities data
df = pd.read_csv('activities.csv')

# Convert date columns
df['created_at'] = pd.to_datetime(df['created_at'])
df['createdAt'] = pd.to_datetime(df['createdAt'])

# Extract date and time components
df['date'] = df['created_at'].dt.date
df['hour'] = df['created_at'].dt.hour
df['day_name'] = df['created_at'].dt.day_name()

print(f"📈 Dataset Overview:")
print(f"Total records: {len(df):,}")
print(f"Unique orders: {df['order_number'].nunique():,}")
print(f"Unique branches: {df['branch'].nunique():,}")
print(f"Unique medications: {df['Activities - OrderId__genericCode'].nunique():,}")

# Calculate daily order losses
daily_orders = df.groupby('date').agg({
    'order_number': 'nunique',
    'Trade Drugs - InternalId__isAvailable': lambda x: (x == False).sum(),
    'Activities - OrderId__genericCode': 'count'
}).rename(columns={
    'order_number': 'total_orders',
    'Trade Drugs - InternalId__isAvailable': 'unavailable_items',
    'Activities - OrderId__genericCode': 'total_items'
})

daily_orders['availability_rate'] = ((daily_orders['total_items'] - daily_orders['unavailable_items']) / daily_orders['total_items'] * 100).round(2)
daily_orders['unavailability_rate'] = (daily_orders['unavailable_items'] / daily_orders['total_items'] * 100).round(2)

print("🚨 DAILY ORDER LOSS SUMMARY")
print("=" * 40)
print(f"Average daily orders lost: {daily_orders['total_orders'].mean():.0f}")
print(f"Average unavailable items per day: {daily_orders['unavailable_items'].mean():.0f}")
print(f"Average availability rate: {daily_orders['availability_rate'].mean():.1f}%")
print(f"Average unavailability rate: {daily_orders['unavailability_rate'].mean():.1f}%")

# Display recent days
print("\n📅 Recent Daily Performance:")
recent_days = daily_orders.tail(7).copy()
recent_days.index = pd.to_datetime(recent_days.index)
recent_days['day_name'] = recent_days.index.strftime('%A')
print(recent_days[['day_name', 'total_orders', 'unavailable_items', 'unavailability_rate']].to_string())

# Plot daily order losses
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

# Daily orders lost
daily_orders['total_orders'].plot(kind='line', ax=ax1, marker='o', color='red', linewidth=2)
ax1.set_title('Daily Orders Lost Due to Medication Unavailability', fontsize=14, fontweight='bold')
ax1.set_ylabel('Number of Orders')
ax1.grid(True, alpha=0.3)
ax1.tick_params(axis='x', rotation=45)

# Daily unavailable items
daily_orders['unavailable_items'].plot(kind='line', ax=ax2, marker='s', color='orange', linewidth=2)
ax2.set_title('Daily Unavailable Medication Items', fontsize=14, fontweight='bold')
ax2.set_ylabel('Number of Items')
ax2.grid(True, alpha=0.3)
ax2.tick_params(axis='x', rotation=45)

# Availability rate
daily_orders['availability_rate'].plot(kind='line', ax=ax3, marker='^', color='green', linewidth=2)
ax3.set_title('Daily Medication Availability Rate', fontsize=14, fontweight='bold')
ax3.set_ylabel('Availability Rate (%)')
ax3.set_ylim(0, 100)
ax3.grid(True, alpha=0.3)
ax3.tick_params(axis='x', rotation=45)

# Unavailability rate
daily_orders['unavailability_rate'].plot(kind='line', ax=ax4, marker='v', color='red', linewidth=2)
ax4.set_title('Daily Medication Unavailability Rate', fontsize=14, fontweight='bold')
ax4.set_ylabel('Unavailability Rate (%)')
ax4.grid(True, alpha=0.3)
ax4.tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# Analyze most frequently unavailable medications
unavailable_meds = df[df['Trade Drugs - InternalId__isAvailable'] == False]

# Top medications by order count affected
top_missing_by_orders = (
    unavailable_meds.groupby('Activities - OrderId__genericCode')['order_number']
    .nunique()
    .reset_index(name='affected_orders')
    .sort_values('affected_orders', ascending=False)
    .head(20)
)

# Top medications by frequency of unavailability
top_missing_by_frequency = (
    unavailable_meds.groupby('Activities - OrderId__genericCode')
    .size()
    .reset_index(name='unavailable_count')
    .sort_values('unavailable_count', ascending=False)
    .head(20)
)

print("💊 TOP MISSING MEDICATIONS - CRITICAL FOR COMMERCIAL TEAM")
print("=" * 60)
print("\n🎯 Top 10 Medications by Number of Orders Affected:")
for i, row in top_missing_by_orders.head(10).iterrows():
    print(f"{row.name + 1:2d}. {row['Activities - OrderId__genericCode']}: {row['affected_orders']} orders lost")

print("\n🔄 Top 10 Medications by Frequency of Unavailability:")
for i, row in top_missing_by_frequency.head(10).iterrows():
    print(f"{row.name + 1:2d}. {row['Activities - OrderId__genericCode']}: {row['unavailable_count']} times unavailable")

# Visualize top missing medications
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))

# Top by orders affected
top_10_orders = top_missing_by_orders.head(10)
bars1 = ax1.barh(range(len(top_10_orders)), top_10_orders['affected_orders'], color='red', alpha=0.7)
ax1.set_yticks(range(len(top_10_orders)))
ax1.set_yticklabels([code[:20] + '...' if len(code) > 20 else code for code in top_10_orders['Activities - OrderId__genericCode']])
ax1.set_xlabel('Number of Orders Affected')
ax1.set_title('Top 10 Medications by Orders Lost', fontsize=14, fontweight='bold')
ax1.grid(True, alpha=0.3)

# Add value labels on bars
for i, bar in enumerate(bars1):
    width = bar.get_width()
    ax1.text(width + 0.5, bar.get_y() + bar.get_height()/2, f'{int(width)}', 
             ha='left', va='center', fontweight='bold')

# Top by frequency
top_10_freq = top_missing_by_frequency.head(10)
bars2 = ax2.barh(range(len(top_10_freq)), top_10_freq['unavailable_count'], color='orange', alpha=0.7)
ax2.set_yticks(range(len(top_10_freq)))
ax2.set_yticklabels([code[:20] + '...' if len(code) > 20 else code for code in top_10_freq['Activities - OrderId__genericCode']])
ax2.set_xlabel('Frequency of Unavailability')
ax2.set_title('Top 10 Medications by Unavailability Frequency', fontsize=14, fontweight='bold')
ax2.grid(True, alpha=0.3)

# Add value labels on bars
for i, bar in enumerate(bars2):
    width = bar.get_width()
    ax2.text(width + 0.5, bar.get_y() + bar.get_height()/2, f'{int(width)}', 
             ha='left', va='center', fontweight='bold')

plt.tight_layout()
plt.show()

# Branch-wise analysis
branch_analysis = df.groupby('branch').agg({
    'order_number': 'nunique',
    'Trade Drugs - InternalId__isAvailable': lambda x: (x == False).sum(),
    'Activities - OrderId__genericCode': 'count'
}).rename(columns={
    'order_number': 'total_orders',
    'Trade Drugs - InternalId__isAvailable': 'unavailable_items',
    'Activities - OrderId__genericCode': 'total_items'
})

branch_analysis['unavailability_rate'] = (branch_analysis['unavailable_items'] / branch_analysis['total_items'] * 100).round(2)
branch_analysis = branch_analysis.sort_values('total_orders', ascending=False)

print("🏪 BRANCH-WISE ORDER LOSS ANALYSIS")
print("=" * 40)
print(f"Total branches affected: {len(branch_analysis)}")
print(f"Average orders per branch: {branch_analysis['total_orders'].mean():.1f}")
print(f"Average unavailability rate per branch: {branch_analysis['unavailability_rate'].mean():.1f}%")

print("\n🔝 Top 10 Branches by Order Volume (Most Affected):")
top_branches = branch_analysis.head(10)
for idx, (branch, row) in enumerate(top_branches.iterrows(), 1):
    print(f"{idx:2d}. {branch}: {row['total_orders']} orders, {row['unavailability_rate']:.1f}% unavailable")

print("\n⚠️  Top 10 Branches by Highest Unavailability Rate:")
worst_branches = branch_analysis.sort_values('unavailability_rate', ascending=False).head(10)
for idx, (branch, row) in enumerate(worst_branches.iterrows(), 1):
    print(f"{idx:2d}. {branch}: {row['unavailability_rate']:.1f}% unavailable ({row['total_orders']} orders)")

# Visualize branch performance
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))

# Top branches by order volume
top_15_branches = branch_analysis.head(15)
bars1 = ax1.bar(range(len(top_15_branches)), top_15_branches['total_orders'], color='blue', alpha=0.7)
ax1.set_xticks(range(len(top_15_branches)))
ax1.set_xticklabels([branch[:10] + '...' if len(branch) > 10 else branch for branch in top_15_branches.index], rotation=45, ha='right')
ax1.set_ylabel('Number of Orders')
ax1.set_title('Top 15 Branches by Order Volume', fontsize=14, fontweight='bold')
ax1.grid(True, alpha=0.3)

# Worst branches by unavailability rate (min 5 orders)
worst_15_branches = branch_analysis[branch_analysis['total_orders'] >= 5].sort_values('unavailability_rate', ascending=False).head(15)
bars2 = ax2.bar(range(len(worst_15_branches)), worst_15_branches['unavailability_rate'], color='red', alpha=0.7)
ax2.set_xticks(range(len(worst_15_branches)))
ax2.set_xticklabels([branch[:10] + '...' if len(branch) > 10 else branch for branch in worst_15_branches.index], rotation=45, ha='right')
ax2.set_ylabel('Unavailability Rate (%)')
ax2.set_title('Top 15 Branches by Unavailability Rate (Min 5 Orders)', fontsize=14, fontweight='bold')
ax2.grid(True, alpha=0.3)

# Scatter plot: Orders vs Unavailability Rate
ax3.scatter(branch_analysis['total_orders'], branch_analysis['unavailability_rate'], alpha=0.6, s=60)
ax3.set_xlabel('Total Orders')
ax3.set_ylabel('Unavailability Rate (%)')
ax3.set_title('Branch Performance: Orders vs Unavailability Rate', fontsize=14, fontweight='bold')
ax3.grid(True, alpha=0.3)

# Distribution of unavailability rates
ax4.hist(branch_analysis['unavailability_rate'], bins=20, color='orange', alpha=0.7, edgecolor='black')
ax4.set_xlabel('Unavailability Rate (%)')
ax4.set_ylabel('Number of Branches')
ax4.set_title('Distribution of Branch Unavailability Rates', fontsize=14, fontweight='bold')
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Hourly analysis
hourly_analysis = df.groupby('hour').agg({
    'order_number': 'nunique',
    'Trade Drugs - InternalId__isAvailable': lambda x: (x == False).sum(),
    'Activities - OrderId__genericCode': 'count'
}).rename(columns={
    'order_number': 'total_orders',
    'Trade Drugs - InternalId__isAvailable': 'unavailable_items',
    'Activities - OrderId__genericCode': 'total_items'
})

hourly_analysis['unavailability_rate'] = (hourly_analysis['unavailable_items'] / hourly_analysis['total_items'] * 100).round(2)

# Day of week analysis
daily_analysis = df.groupby('day_name').agg({
    'order_number': 'nunique',
    'Trade Drugs - InternalId__isAvailable': lambda x: (x == False).sum(),
    'Activities - OrderId__genericCode': 'count'
}).rename(columns={
    'order_number': 'total_orders',
    'Trade Drugs - InternalId__isAvailable': 'unavailable_items',
    'Activities - OrderId__genericCode': 'total_items'
})

daily_analysis['unavailability_rate'] = (daily_analysis['unavailable_items'] / daily_analysis['total_items'] * 100).round(2)

# Reorder days of week
day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
daily_analysis = daily_analysis.reindex([day for day in day_order if day in daily_analysis.index])

print("⏰ TIME-BASED ORDER LOSS PATTERNS")
print("=" * 40)
print("\n🕐 Peak Hours for Order Losses:")
peak_hours = hourly_analysis.sort_values('total_orders', ascending=False).head(5)
for hour, row in peak_hours.iterrows():
    print(f"   {hour:2d}:00 - {row['total_orders']} orders ({row['unavailability_rate']:.1f}% unavailable)")

print("\n📅 Day of Week Performance:")
for day, row in daily_analysis.iterrows():
    print(f"   {day}: {row['total_orders']} orders ({row['unavailability_rate']:.1f}% unavailable)")

# Visualize time patterns
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 12))

# Hourly order losses
ax1.plot(hourly_analysis.index, hourly_analysis['total_orders'], marker='o', linewidth=2, markersize=6, color='red')
ax1.set_xlabel('Hour of Day')
ax1.set_ylabel('Number of Orders Lost')
ax1.set_title('Hourly Order Losses', fontsize=14, fontweight='bold')
ax1.grid(True, alpha=0.3)
ax1.set_xticks(range(0, 24, 2))

# Hourly unavailability rate
ax2.plot(hourly_analysis.index, hourly_analysis['unavailability_rate'], marker='s', linewidth=2, markersize=6, color='orange')
ax2.set_xlabel('Hour of Day')
ax2.set_ylabel('Unavailability Rate (%)')
ax2.set_title('Hourly Unavailability Rate', fontsize=14, fontweight='bold')
ax2.grid(True, alpha=0.3)
ax2.set_xticks(range(0, 24, 2))

# Daily order losses
bars3 = ax3.bar(range(len(daily_analysis)), daily_analysis['total_orders'], color='blue', alpha=0.7)
ax3.set_xticks(range(len(daily_analysis)))
ax3.set_xticklabels(daily_analysis.index, rotation=45)
ax3.set_ylabel('Number of Orders Lost')
ax3.set_title('Daily Order Losses by Day of Week', fontsize=14, fontweight='bold')
ax3.grid(True, alpha=0.3)

# Daily unavailability rate
bars4 = ax4.bar(range(len(daily_analysis)), daily_analysis['unavailability_rate'], color='green', alpha=0.7)
ax4.set_xticks(range(len(daily_analysis)))
ax4.set_xticklabels(daily_analysis.index, rotation=45)
ax4.set_ylabel('Unavailability Rate (%)')
ax4.set_title('Daily Unavailability Rate by Day of Week', fontsize=14, fontweight='bold')
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Financial impact estimation
# Note: These are estimated values - adjust based on actual business metrics
ESTIMATED_ORDER_VALUE = 150  # Average order value in SAR
ESTIMATED_MARGIN = 0.25      # 25% profit margin

total_orders_lost = df['order_number'].nunique()
total_items_unavailable = (df['Trade Drugs - InternalId__isAvailable'] == False).sum()
total_items = len(df)

# Calculate financial impact
estimated_revenue_loss = total_orders_lost * ESTIMATED_ORDER_VALUE
estimated_profit_loss = estimated_revenue_loss * ESTIMATED_MARGIN

# Daily averages
days_in_analysis = (df['date'].max() - df['date'].min()).days + 1
daily_revenue_loss = estimated_revenue_loss / days_in_analysis
daily_profit_loss = estimated_profit_loss / days_in_analysis

# Monthly and yearly projections
monthly_revenue_loss = daily_revenue_loss * 30
monthly_profit_loss = daily_profit_loss * 30
yearly_revenue_loss = daily_revenue_loss * 365
yearly_profit_loss = daily_profit_loss * 365

print("💰 FINANCIAL IMPACT ANALYSIS")
print("=" * 40)
print(f"Analysis period: {days_in_analysis} days")
print(f"Assumptions: Avg order value = {ESTIMATED_ORDER_VALUE} SAR, Profit margin = {ESTIMATED_MARGIN*100}%")
print()
print("📊 TOTAL IMPACT (Analysis Period):")
print(f"   Orders lost: {total_orders_lost:,}")
print(f"   Items unavailable: {total_items_unavailable:,}")
print(f"   Estimated revenue loss: {estimated_revenue_loss:,.0f} SAR")
print(f"   Estimated profit loss: {estimated_profit_loss:,.0f} SAR")
print()
print("📈 DAILY AVERAGES:")
print(f"   Orders lost per day: {total_orders_lost/days_in_analysis:.1f}")
print(f"   Revenue loss per day: {daily_revenue_loss:,.0f} SAR")
print(f"   Profit loss per day: {daily_profit_loss:,.0f} SAR")
print()
print("🎯 PROJECTIONS:")
print(f"   Monthly revenue loss: {monthly_revenue_loss:,.0f} SAR")
print(f"   Monthly profit loss: {monthly_profit_loss:,.0f} SAR")
print(f"   Yearly revenue loss: {yearly_revenue_loss:,.0f} SAR")
print(f"   Yearly profit loss: {yearly_profit_loss:,.0f} SAR")

# Detailed medication analysis
medication_analysis = df.groupby('Activities - OrderId__genericCode').agg({
    'order_number': 'nunique',
    'Trade Drugs - InternalId__isAvailable': ['count', lambda x: (x == True).sum(), lambda x: (x == False).sum()],
    'Trade Drugs - InternalId__quantity': 'sum',
    'Trade Drugs - InternalId__quantityAvailable': 'sum'
}).round(2)

# Flatten column names
medication_analysis.columns = ['orders_affected', 'total_requests', 'available_requests', 'unavailable_requests', 'quantity_requested', 'quantity_available']
medication_analysis['availability_rate'] = (medication_analysis['available_requests'] / medication_analysis['total_requests'] * 100).round(2)
medication_analysis['quantity_fulfillment_rate'] = (medication_analysis['quantity_available'] / medication_analysis['quantity_requested'] * 100).round(2)

# Sort by orders affected
medication_analysis = medication_analysis.sort_values('orders_affected', ascending=False)

print("💊 DETAILED MEDICATION AVAILABILITY ANALYSIS")
print("=" * 50)
print(f"Total unique medications: {len(medication_analysis)}")
print(f"Average availability rate: {medication_analysis['availability_rate'].mean():.1f}%")
print(f"Average quantity fulfillment rate: {medication_analysis['quantity_fulfillment_rate'].mean():.1f}%")

print("\n🚨 CRITICAL MEDICATIONS (Top 15 by Orders Affected):")
critical_meds = medication_analysis.head(15)
for idx, (med, row) in enumerate(critical_meds.iterrows(), 1):
    print(f"{idx:2d}. {med[:50]}..." if len(med) > 50 else f"{idx:2d}. {med}")
    print(f"    Orders affected: {row['orders_affected']}, Availability: {row['availability_rate']:.1f}%, Qty fulfillment: {row['quantity_fulfillment_rate']:.1f}%")

print("\n⚠️  WORST PERFORMING MEDICATIONS (Lowest Availability, Min 5 Orders):")
worst_meds = medication_analysis[medication_analysis['orders_affected'] >= 5].sort_values('availability_rate').head(10)
for idx, (med, row) in enumerate(worst_meds.iterrows(), 1):
    print(f"{idx:2d}. {med[:50]}..." if len(med) > 50 else f"{idx:2d}. {med}")
    print(f"    Availability: {row['availability_rate']:.1f}%, Orders affected: {row['orders_affected']}")

# Generate executive summary
total_unique_orders = df['order_number'].nunique()
total_unique_meds = df['Activities - OrderId__genericCode'].nunique()
total_unique_branches = df['branch'].nunique()
overall_unavailability_rate = (df['Trade Drugs - InternalId__isAvailable'] == False).sum() / len(df) * 100

# Top problematic medication
top_problem_med = top_missing_by_orders.iloc[0]
top_problem_branch = branch_analysis.sort_values('unavailability_rate', ascending=False).iloc[0]

print("📋 EXECUTIVE SUMMARY - DAILY ORDER LOSS ANALYSIS")
print("=" * 60)
print(f"Analysis Period: {df['date'].min()} to {df['date'].max()} ({days_in_analysis} days)")
print()
print("🔢 KEY METRICS:")
print(f"   • Total orders lost: {total_unique_orders:,}")
print(f"   • Unique medications involved: {total_unique_meds:,}")
print(f"   • Branches affected: {total_unique_branches:,}")
print(f"   • Overall unavailability rate: {overall_unavailability_rate:.1f}%")
print(f"   • Daily average orders lost: {total_unique_orders/days_in_analysis:.1f}")
print()
print("💰 FINANCIAL IMPACT:")
print(f"   • Estimated daily revenue loss: {daily_revenue_loss:,.0f} SAR")
print(f"   • Estimated monthly revenue loss: {monthly_revenue_loss:,.0f} SAR")
print(f"   • Estimated yearly revenue loss: {yearly_revenue_loss:,.0f} SAR")
print()
print("🎯 TOP ISSUES:")
print(f"   • Most problematic medication: {top_problem_med['Activities - OrderId__genericCode'][:40]}...")
print(f"     (Affects {top_problem_med['affected_orders']} orders)")
print(f"   • Branch with highest unavailability: {top_problem_branch.name}")
print(f"     ({top_problem_branch['unavailability_rate']:.1f}% unavailable)")
print()
print("📈 RECOMMENDATIONS FOR COMMERCIAL TEAM:")
print("   1. 🎯 PRIORITY ACTIONS:")
print(f"      • Focus on top 10 medications causing {top_missing_by_orders.head(10)['affected_orders'].sum()} order losses")
print(f"      • Address inventory issues in top 5 branches with highest order volumes")
print(f"      • Implement daily monitoring for medications with <50% availability rate")
print()
print("   2. 📊 OPERATIONAL IMPROVEMENTS:")
print("      • Set up automated alerts for critical medication stock levels")
print("      • Establish backup supplier relationships for high-demand medications")
print("      • Implement predictive inventory management based on historical patterns")
print()
print("   3. 💼 BUSINESS IMPACT MITIGATION:")
print("      • Develop alternative medication suggestions for unavailable items")
print("      • Create customer communication templates for stock-out situations")
print(f"      • Target reducing daily order losses by 50% to save ~{daily_revenue_loss/2:,.0f} SAR/day")
print()
print("📞 NEXT STEPS:")
print("   • Schedule weekly review meetings with supply chain team")
print("   • Implement real-time inventory tracking dashboard")
print("   • Set up customer retention programs for affected orders")
print("   • Monitor progress with monthly order loss reduction targets")

