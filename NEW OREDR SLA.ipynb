{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"collapsed": true}, "outputs": [], "source": ["from datetime import timedelta\n", "\n", "import pandas as pd\n", "\n", "from data import get_consolidated_orders\n", "from data import get_wasfaty_raw_leads"]}, {"cell_type": "code", "execution_count": 2, "id": "6a697f866ec56383", "metadata": {}, "outputs": [], "source": ["# Create a config for wasfatydb (activities DB)\n", "wasfatydb_config = {\n", "    \"db_host\": \"************\",  # Update if different for wasfatydb\n", "    \"db_port\": \"5432\",\n", "    \"db_name\": \"wasfatydb\",  # Use the correct DB name for activities\n", "    \"db_user\": \"product_service_user\",\n", "    \"db_pass\": \"_&kJCD>>LJ<oTkUp\"\n", "}\n"]}, {"cell_type": "code", "execution_count": 3, "id": "a02272d5ad72f171", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Raw Leads Query SELECT *\n", "               FROM \"public\".\"leads\" l\n", "               WHERE l.\"status\" NOT IN ('failed', 'rejected', 'new', 'inactive', 'canceled')  AND l.\"createdAt\" >= TIMESTAMP '2025-06-01 00:00:00.000+03:00' AND l.\"createdAt\" < TIMESTAMP '2025-06-30 00:00:00.000+03:00'\n", "ORDER BY l.\"createdAt\" DESC\n"]}], "source": ["# Get the raw leads data from Wasfaty Microservice\n", "leads = get_wasfaty_raw_leads(wasfatydb_config,\n", "                              start_date='2025-06-01',\n", "                              end_date='2025-06-30')\n", "# Convert to DataFrame\n", "leads_df = pd.DataFrame(leads)"]}, {"cell_type": "code", "execution_count": 4, "id": "68107b05852122f8", "metadata": {}, "outputs": [], "source": ["# Apply the order type based on the tags if it is express_delivery or scheduled_delivery then apply the type express or scheduled\n", "leads_df['order_type'] = leads_df['tags'].apply(\n", "    lambda x: 'express' if 'express_delivery' in x else 'scheduled' if 'scheduled_delivery' in x else None)\n", "\n", "# Apply the warehouse based on the tags if it 7 then we call it Damman if its is 10 we call it Riyadh\n", "leads_df['warehouse'] = leads_df['warehouseIds'].apply(\n", "    lambda x: '<PERSON><PERSON><PERSON>' if 7 in x else 'Riyadh' if 10 in x else None)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "179a4ed322e97368", "metadata": {}, "outputs": [], "source": ["# Safely cast time objects to strings\n", "leads_df['deliveryTimeSlotStartTime'] = leads_df['deliveryTimeSlotStartTime'].apply(\n", "    lambda x: x.strftime('%H:%M:%S') if isinstance(x, pd._libs.tslibs.timestamps.Timestamp) or isinstance(x,\n", "                                                                                                          pd.Timestamp) else (\n", "        x.strftime('%H:%M:%S') if hasattr(x, 'strftime') else x)\n", ")\n", "\n", "leads_df['deliveryTimeSlotEndTime'] = leads_df['deliveryTimeSlotEndTime'].apply(\n", "    lambda x: x.strftime('%H:%M:%S') if isinstance(x, pd._libs.tslibs.timestamps.Timestamp) or isinstance(x,\n", "                                                                                                          pd.Timestamp) else (\n", "        x.strftime('%H:%M:%S') if hasattr(x, 'strftime') else x)\n", ")\n", "\n", "# Convert original delivery time slot columns to timedelta\n", "leads_df['start_time'] = pd.to_timedelta(leads_df['deliveryTimeSlotStartTime'], errors='coerce')\n", "leads_df['end_time'] = pd.to_timedelta(leads_df['deliveryTimeSlotEndTime'], errors='coerce')\n", "\n", "\n", "# Recalculate delivery window based on updated time columns\n", "def recalculate_delivery_window(row):\n", "    if row['order_type'] == 'express':\n", "        start = row['createdAt']\n", "        end = start + <PERSON><PERSON><PERSON>(hours=3) if pd.notnull(start) else pd.NaT\n", "    elif pd.notnull(row['deliveryDate']) and pd.notnull(row['start_time']) and pd.notnull(row['end_time']):\n", "        start = row['deliveryDate'] + row['start_time']\n", "        end = row['deliveryDate'] + row['end_time']\n", "    else:\n", "        start = end = pd.NaT\n", "    return pd.Series([start, end], index=['delivery_start_datetime', 'delivery_end_datetime'])\n", "\n", "\n", "# Apply updated logic\n", "leads_df[['delivery_start_datetime', 'delivery_end_datetime']] = leads_df.apply(recalculate_delivery_window, axis=1)\n", "\n", "# drop the columns that are not needed only keep delivery_start_datetime, delivery_end_datetime, order_type, warehouse, orderId,erxReference,orderNumber,status,branchLicense\n", "leads_df = leads_df[\n", "    ['orderId', 'erxReference', 'orderNumber', 'delivery_start_datetime', 'delivery_end_datetime', 'order_type',\n", "     'warehouse', 'status', 'branchLicense']]\n", "leads_df['order_number'] = leads_df['orderNumber']\n"]}, {"cell_type": "code", "execution_count": 6, "id": "1e64b6b9fe5d3f34", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Consolidated Orders Query SELECT *\n", "               FROM \"public\".\"consolidated_orders\" co\n", "               where co.\"fulfillment_status\" = 'fulfilled'  AND co.\"created_at\" >= TIMESTAMP '2025-06-01 00:00:00.000+03:00' AND co.\"created_at\" < TIMESTAMP '2025-06-30 00:00:00.000+03:00'\n", "ORDER BY co.\"created_at\" DESC\n"]}], "source": ["# Get consolidated order data for this month and then left join it with the leads_df\n", "DWH_CONFIGS = wasfatydb_config.copy()  # Copy the config to avoid modifying the original\n", "DWH_CONFIGS['db_name'] = 'General DWH'  # Use the correct DB name for activities\n", "\n", "consolidated_orders = get_consolidated_orders(DWH_CONFIGS,\n", "                                              start_date='2025-06-01',\n", "                                              end_date='2025-06-30')\n"]}, {"cell_type": "code", "execution_count": 7, "id": "ebea93aa119f4a8d", "metadata": {}, "outputs": [], "source": ["# left join the consolidated orders with the leads_df\n", "final_df = leads_df.merge(consolidated_orders, how='left', left_on='order_number', right_on='order_number',\n", "                          suffixes=('', '_consolidated'))\n"]}, {"cell_type": "code", "execution_count": 8, "id": "5c5bbc3dcf3caadb", "metadata": {}, "outputs": [], "source": ["# filter order which heave either first_attempt_at or delivered_at\n", "final_df = final_df[\n", "    (final_df['first_attempt_at'].notnull()) | (final_df['delivered_at'].notnull())]\n"]}, {"cell_type": "code", "execution_count": 9, "id": "1e21762fa4c06732", "metadata": {}, "outputs": [], "source": ["# create a column called sla_date, if first_attempt_at is not null then sla_date = first_attempt_at else sla_date = delivered_at\n", "final_df['sla_date'] = final_df.apply(\n", "    lambda x: x['first_attempt_at'] if pd.notnull(x['first_attempt_at']) else x['delivered_at'], axis=1)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "id": "ed6cf668c320ffc1", "metadata": {}, "outputs": [], "source": ["final_df.to_csv('wasfaty_sla_data.csv', index=False)"]}, {"cell_type": "code", "execution_count": 11, "id": "93a2481018eca3af", "metadata": {"ExecuteTime": {"end_time": "2025-06-17T13:20:56.673182Z", "start_time": "2025-06-17T13:20:56.614633Z"}}, "outputs": [], "source": ["# calculate if SLA compliance\n", "# if the order is express then the sla_date should be less than or equal to  createdAt + 3 hours\n", "# if the order is scheduled then the sla_date should be between delivery_start_datetime and delivery_end_datetime\n", "# Reapply SLA compliance logic\n", "def check_sla_compliance(row):\n", "    try:\n", "        sla_time = pd.to_datetime(row['sla_date'], errors='coerce')\n", "        if row['order_type'] == 'express':\n", "            return sla_time <= row['created_at'] + timed<PERSON><PERSON>(hours=3) if pd.notnull(sla_time) and pd.notnull(row['created_at']) else False\n", "        elif pd.notnull(row['delivery_start_datetime']) and pd.notnull(row['delivery_end_datetime']):\n", "            return row['delivery_start_datetime'] <= sla_time <= row['delivery_end_datetime'] if pd.notnull(sla_time) else False\n", "        return False\n", "    except Exception:\n", "        return False\n", "\n", "final_df['sla_compliance'] = final_df.apply(check_sla_compliance, axis=1)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "id": "1ffe01293e9a90f", "metadata": {"ExecuteTime": {"end_time": "2025-06-17T13:21:22.294631Z", "start_time": "2025-06-17T13:21:22.243569Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>orderId</th>\n", "      <th>erxReference</th>\n", "      <th>orderNumber</th>\n", "      <th>delivery_start_datetime</th>\n", "      <th>delivery_end_datetime</th>\n", "      <th>order_type</th>\n", "      <th>warehouse</th>\n", "      <th>status</th>\n", "      <th>branchLicense</th>\n", "      <th>order_number</th>\n", "      <th>...</th>\n", "      <th>delivery_start</th>\n", "      <th>delivery_end</th>\n", "      <th>is_sla_met</th>\n", "      <th>expected_delivery_in</th>\n", "      <th>fulfilled_in</th>\n", "      <th>confirmed_in</th>\n", "      <th>delivered_in</th>\n", "      <th>completed_in</th>\n", "      <th>sla_date</th>\n", "      <th>sla_compliance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>198</th>\n", "      <td>T3JkZXI6MTUwMDk3OTU=</td>\n", "      <td>l1993423</td>\n", "      <td>15009795</td>\n", "      <td>2025-06-26 15:06:17.174838+00:00</td>\n", "      <td>2025-06-26 18:06:17.174838+00:00</td>\n", "      <td>express</td>\n", "      <td>Dammam</td>\n", "      <td>delivered</td>\n", "      <td>PHARMACIATY37</td>\n", "      <td>15009795</td>\n", "      <td>...</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>no</td>\n", "      <td>180.000000</td>\n", "      <td>1.483333</td>\n", "      <td>34.671550</td>\n", "      <td>277.899700</td>\n", "      <td>279.383033</td>\n", "      <td>2025-06-26 19:46:34.982000+00:00</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199</th>\n", "      <td>T3JkZXI6MTUwMDk3MTM=</td>\n", "      <td>k0519340</td>\n", "      <td>15009713</td>\n", "      <td>2025-06-26 15:04:03.044991+00:00</td>\n", "      <td>2025-06-26 18:04:03.044991+00:00</td>\n", "      <td>express</td>\n", "      <td>Dammam</td>\n", "      <td>delivered</td>\n", "      <td>PHARMACIATY49</td>\n", "      <td>15009713</td>\n", "      <td>...</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>no</td>\n", "      <td>180.000000</td>\n", "      <td>1.683333</td>\n", "      <td>53.759967</td>\n", "      <td>229.604200</td>\n", "      <td>231.287533</td>\n", "      <td>2025-06-26 18:55:45.252000+00:00</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>T3JkZXI6MTUwMDk2NTg=</td>\n", "      <td>u1995923</td>\n", "      <td>15009658</td>\n", "      <td>2025-06-26 15:02:21.348547+00:00</td>\n", "      <td>2025-06-26 18:02:21.348547+00:00</td>\n", "      <td>express</td>\n", "      <td>Dammam</td>\n", "      <td>delivered</td>\n", "      <td>PHARMACIATY43</td>\n", "      <td>15009658</td>\n", "      <td>...</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>yes</td>\n", "      <td>180.000000</td>\n", "      <td>2.266667</td>\n", "      <td>53.094533</td>\n", "      <td>99.540833</td>\n", "      <td>192.900917</td>\n", "      <td>2025-06-26 16:44:40.450000+00:00</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>T3JkZXI6MTUwMDk2MTI=</td>\n", "      <td>b3999288</td>\n", "      <td>15009612</td>\n", "      <td>2025-06-26 15:01:48.641922+00:00</td>\n", "      <td>2025-06-26 18:01:48.641922+00:00</td>\n", "      <td>express</td>\n", "      <td>Dammam</td>\n", "      <td>delivered</td>\n", "      <td>PHARMACIATY12</td>\n", "      <td>15009612</td>\n", "      <td>...</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>no</td>\n", "      <td>180.000000</td>\n", "      <td>3.016667</td>\n", "      <td>64.868933</td>\n", "      <td>209.213250</td>\n", "      <td>212.229917</td>\n", "      <td>2025-06-26 18:35:12.795000+00:00</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>T3JkZXI6MTUwMDk1ODE=</td>\n", "      <td>f3341463</td>\n", "      <td>15009581</td>\n", "      <td>2025-06-26 15:01:11.423800+00:00</td>\n", "      <td>2025-06-26 18:01:11.423800+00:00</td>\n", "      <td>express</td>\n", "      <td>Dammam</td>\n", "      <td>delivered</td>\n", "      <td>PHARMACIATY98</td>\n", "      <td>15009581</td>\n", "      <td>...</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>yes</td>\n", "      <td>180.000000</td>\n", "      <td>1.333333</td>\n", "      <td>91.548617</td>\n", "      <td>100.118950</td>\n", "      <td>248.207417</td>\n", "      <td>2025-06-26 16:43:35.137000+00:00</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4968</th>\n", "      <td>T3JkZXI6MTM3MDE0ODA=</td>\n", "      <td>t5693541</td>\n", "      <td>13701480</td>\n", "      <td>2025-06-02</td>\n", "      <td>2025-06-02</td>\n", "      <td>scheduled</td>\n", "      <td>Dammam</td>\n", "      <td>delivered</td>\n", "      <td>PHARMACIATY51</td>\n", "      <td>13701480</td>\n", "      <td>...</td>\n", "      <td>2025-06-02 06:00:00+00:00</td>\n", "      <td>2025-06-02 08:00:00+00:00</td>\n", "      <td>no</td>\n", "      <td>1553.453735</td>\n", "      <td>34.800000</td>\n", "      <td>49.108550</td>\n", "      <td>2048.646867</td>\n", "      <td>2083.446867</td>\n", "      <td>2025-06-02 16:50:10.812000+00:00</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4969</th>\n", "      <td>T3JkZXI6MTM3MDE0NDc=</td>\n", "      <td>m0836936</td>\n", "      <td>13701447</td>\n", "      <td>2025-06-01</td>\n", "      <td>2025-06-01</td>\n", "      <td>scheduled</td>\n", "      <td>Dammam</td>\n", "      <td>delivered</td>\n", "      <td>PHARMACIATY28</td>\n", "      <td>13701447</td>\n", "      <td>...</td>\n", "      <td>2025-06-01 16:00:00+00:00</td>\n", "      <td>2025-06-01 18:00:00+00:00</td>\n", "      <td>yes</td>\n", "      <td>714.589675</td>\n", "      <td>36.283333</td>\n", "      <td>21.156750</td>\n", "      <td>243.787017</td>\n", "      <td>280.070350</td>\n", "      <td>2025-06-01 10:46:12.221000+00:00</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4970</th>\n", "      <td>T3JkZXI6MTM3MDE0MjU=</td>\n", "      <td>h9737716</td>\n", "      <td>13701425</td>\n", "      <td>2025-06-01</td>\n", "      <td>2025-06-01</td>\n", "      <td>scheduled</td>\n", "      <td>Dammam</td>\n", "      <td>delivered</td>\n", "      <td>PHARMACIATY103</td>\n", "      <td>13701425</td>\n", "      <td>...</td>\n", "      <td>2025-06-01 14:00:00+00:00</td>\n", "      <td>2025-06-01 16:00:00+00:00</td>\n", "      <td>yes</td>\n", "      <td>597.105623</td>\n", "      <td>41.033333</td>\n", "      <td>22.898917</td>\n", "      <td>156.735317</td>\n", "      <td>197.768650</td>\n", "      <td>2025-06-01 09:20:49.119000+00:00</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4971</th>\n", "      <td>T3JkZXI6MTM3MDAyODE=</td>\n", "      <td>k8980765</td>\n", "      <td>13700281</td>\n", "      <td>2025-06-01</td>\n", "      <td>2025-06-01</td>\n", "      <td>scheduled</td>\n", "      <td>Riyadh</td>\n", "      <td>delivered</td>\n", "      <td>PHARMACIATY276</td>\n", "      <td>13700281</td>\n", "      <td>...</td>\n", "      <td>2025-06-01 06:00:00+00:00</td>\n", "      <td>2025-06-01 08:00:00+00:00</td>\n", "      <td>no</td>\n", "      <td>161.859066</td>\n", "      <td>128.016667</td>\n", "      <td>64.633333</td>\n", "      <td>187.564033</td>\n", "      <td>315.580700</td>\n", "      <td>2025-06-01 10:33:45.842000+00:00</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4972</th>\n", "      <td>T3JkZXI6MTM3MDAyMzY=</td>\n", "      <td>c3260556</td>\n", "      <td>13700236</td>\n", "      <td>2025-06-01</td>\n", "      <td>2025-06-01</td>\n", "      <td>scheduled</td>\n", "      <td>Riyadh</td>\n", "      <td>delivered</td>\n", "      <td>PHARMACIATY276</td>\n", "      <td>13700236</td>\n", "      <td>...</td>\n", "      <td>2025-06-01 06:00:00+00:00</td>\n", "      <td>2025-06-01 08:00:00+00:00</td>\n", "      <td>no</td>\n", "      <td>162.973132</td>\n", "      <td>128.500000</td>\n", "      <td>100.152633</td>\n", "      <td>195.105983</td>\n", "      <td>323.605983</td>\n", "      <td>2025-06-01 10:40:54.359000+00:00</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4753 rows × 40 columns</p>\n", "</div>"], "text/plain": ["                   orderId erxReference orderNumber  \\\n", "198   T3JkZXI6MTUwMDk3OTU=     l1993423    15009795   \n", "199   T3JkZXI6MTUwMDk3MTM=     k0519340    15009713   \n", "200   T3JkZXI6MTUwMDk2NTg=     u1995923    15009658   \n", "201   T3JkZXI6MTUwMDk2MTI=     b3999288    15009612   \n", "202   T3JkZXI6MTUwMDk1ODE=     f3341463    15009581   \n", "...                    ...          ...         ...   \n", "4968  T3JkZXI6MTM3MDE0ODA=     t5693541    13701480   \n", "4969  T3JkZXI6MTM3MDE0NDc=     m0836936    13701447   \n", "4970  T3JkZXI6MTM3MDE0MjU=     h9737716    13701425   \n", "4971  T3JkZXI6MTM3MDAyODE=     k8980765    13700281   \n", "4972  T3JkZXI6MTM3MDAyMzY=     c3260556    13700236   \n", "\n", "               delivery_start_datetime             delivery_end_datetime  \\\n", "198   2025-06-26 15:06:17.174838+00:00  2025-06-26 18:06:17.174838+00:00   \n", "199   2025-06-26 15:04:03.044991+00:00  2025-06-26 18:04:03.044991+00:00   \n", "200   2025-06-26 15:02:21.348547+00:00  2025-06-26 18:02:21.348547+00:00   \n", "201   2025-06-26 15:01:48.641922+00:00  2025-06-26 18:01:48.641922+00:00   \n", "202   2025-06-26 15:01:11.423800+00:00  2025-06-26 18:01:11.423800+00:00   \n", "...                                ...                               ...   \n", "4968                        2025-06-02                        2025-06-02   \n", "4969                        2025-06-01                        2025-06-01   \n", "4970                        2025-06-01                        2025-06-01   \n", "4971                        2025-06-01                        2025-06-01   \n", "4972                        2025-06-01                        2025-06-01   \n", "\n", "     order_type warehouse     status   branchLicense order_number  ...  \\\n", "198     express    Dammam  delivered   PHARMACIATY37     15009795  ...   \n", "199     express    Dammam  delivered   PHARMACIATY49     15009713  ...   \n", "200     express    Dammam  delivered   PHARMACIATY43     15009658  ...   \n", "201     express    Dammam  delivered   PHARMACIATY12     15009612  ...   \n", "202     express    Dammam  delivered   PHARMACIATY98     15009581  ...   \n", "...         ...       ...        ...             ...          ...  ...   \n", "4968  scheduled    Dammam  delivered   PHARMACIATY51     13701480  ...   \n", "4969  scheduled    Dammam  delivered   PHARMACIATY28     13701447  ...   \n", "4970  scheduled    Dammam  delivered  PHARMACIATY103     13701425  ...   \n", "4971  scheduled    Riyadh  delivered  PHARMACIATY276     13700281  ...   \n", "4972  scheduled    Riyadh  delivered  PHARMACIATY276     13700236  ...   \n", "\n", "                delivery_start              delivery_end is_sla_met  \\\n", "198                        NaT                       NaT         no   \n", "199                        NaT                       NaT         no   \n", "200                        NaT                       NaT        yes   \n", "201                        NaT                       NaT         no   \n", "202                        NaT                       NaT        yes   \n", "...                        ...                       ...        ...   \n", "4968 2025-06-02 06:00:00+00:00 2025-06-02 08:00:00+00:00         no   \n", "4969 2025-06-01 16:00:00+00:00 2025-06-01 18:00:00+00:00        yes   \n", "4970 2025-06-01 14:00:00+00:00 2025-06-01 16:00:00+00:00        yes   \n", "4971 2025-06-01 06:00:00+00:00 2025-06-01 08:00:00+00:00         no   \n", "4972 2025-06-01 06:00:00+00:00 2025-06-01 08:00:00+00:00         no   \n", "\n", "     expected_delivery_in fulfilled_in confirmed_in delivered_in  \\\n", "198            180.000000     1.483333    34.671550   277.899700   \n", "199            180.000000     1.683333    53.759967   229.604200   \n", "200            180.000000     2.266667    53.094533    99.540833   \n", "201            180.000000     3.016667    64.868933   209.213250   \n", "202            180.000000     1.333333    91.548617   100.118950   \n", "...                   ...          ...          ...          ...   \n", "4968          1553.453735    34.800000    49.108550  2048.646867   \n", "4969           714.589675    36.283333    21.156750   243.787017   \n", "4970           597.105623    41.033333    22.898917   156.735317   \n", "4971           161.859066   128.016667    64.633333   187.564033   \n", "4972           162.973132   128.500000   100.152633   195.105983   \n", "\n", "      completed_in                         sla_date sla_compliance  \n", "198     279.383033 2025-06-26 19:46:34.982000+00:00          False  \n", "199     231.287533 2025-06-26 18:55:45.252000+00:00          False  \n", "200     192.900917 2025-06-26 16:44:40.450000+00:00           True  \n", "201     212.229917 2025-06-26 18:35:12.795000+00:00          False  \n", "202     248.207417 2025-06-26 16:43:35.137000+00:00           True  \n", "...            ...                              ...            ...  \n", "4968   2083.446867 2025-06-02 16:50:10.812000+00:00          False  \n", "4969    280.070350 2025-06-01 10:46:12.221000+00:00          False  \n", "4970    197.768650 2025-06-01 09:20:49.119000+00:00          False  \n", "4971    315.580700 2025-06-01 10:33:45.842000+00:00          False  \n", "4972    323.605983 2025-06-01 10:40:54.359000+00:00          False  \n", "\n", "[4753 rows x 40 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["final_df"]}, {"cell_type": "code", "execution_count": null, "id": "b81270cdd7cdbb95", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 5}