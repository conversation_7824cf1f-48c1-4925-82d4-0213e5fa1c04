Metadata-Version: 2.4
Name: folium
Version: 0.20.0
Summary: Make beautiful maps with Leaflet.js & Python
Home-page: https://github.com/python-visualization/folium
Author: <PERSON> Story
Author-email: <EMAIL>
License: MIT
Keywords: data visualization
Platform: any
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Scientific/Engineering :: GIS
Classifier: Topic :: Scientific/Engineering :: Visualization
Classifier: License :: OSI Approved :: MIT License
Classifier: Development Status :: 5 - Production/Stable
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
Requires-Dist: branca>=0.6.0
Requires-Dist: jinja2>=2.9
Requires-Dist: numpy
Requires-Dist: requests
Requires-Dist: xyzservices
Provides-Extra: testing
Requires-Dist: pytest; extra == "testing"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: platform
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

|PyPI| |Test| |Gitter| |DOI| |binder|

.. |PyPI| image:: https://img.shields.io/pypi/v/folium.svg
    :target: https://pypi.org/project/folium
    :alt: PyPI Package

.. |Test| image:: https://github.com/python-visualization/folium/actions/workflows/test_code.yml/badge.svg
    :target: https://github.com/python-visualization/folium/actions/workflows/test_code.yml
    :alt: Code tests

.. |Gitter| image:: https://badges.gitter.im/python-visualization/folium.svg
    :target: https://gitter.im/python-visualization/folium
    :alt: Gitter

.. |DOI| image:: https://zenodo.org/badge/18669/python-visualization/folium.svg
   :target: https://zenodo.org/badge/latestdoi/18669/python-visualization/folium
   :alt: DOI

.. |binder| image:: https://mybinder.org/badge_logo.svg
 :target: https://mybinder.org/v2/gh/python-visualization/folium/main?filepath=examples

folium
======

.. image:: https://github.com/python-visualization/folium/blob/main/docs/_static/folium_logo.png
   :height: 100px


Python Data, Leaflet.js Maps
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

`folium` builds on the data wrangling strengths of the Python ecosystem and the
mapping strengths of the Leaflet.js library. Manipulate your data in Python,
then visualize it in a Leaflet map via `folium`.

Installation
------------

.. code:: bash

    $ pip install folium

or

.. code:: bash

    $ conda install -c conda-forge folium

Documentation
-------------

https://python-visualization.github.io/folium/latest/


Contributing
------------

We love contributions!  folium is open source, built on open source,
and we'd love to have you hang out in our community.

See `our complete contributor's guide <https://github.com/python-visualization/folium/blob/main/.github/CONTRIBUTING.md>`_ for more info.


Changelog
---------

- Release notes of v0.16.0 and higher: https://github.com/python-visualization/folium/releases
- Older `changelog <https://raw.githubusercontent.com/python-visualization/folium/main/CHANGES.txt>`_


Packages and plugins
--------------------

Packages:

- https://github.com/geopandas/xyzservices: a repository of raster basemap tilesets.
- https://github.com/randyzwitch/streamlit-folium: run folium in a Streamlit app.
- https://github.com/FEMlium/FEMlium: interactive visualization of finite element simulations on geographic maps with folium.

Plugins:

- https://github.com/onaci/folium-glify-layer: provide fast webgl rendering for large GeoJSON FeatureCollections
- https://github.com/carlosign/Folium.ControlCredits-Plugin: displaying credits in the corner. Display an image/logo, clicking it will expand to show a brief message with credits and links.
- https://github.com/JohnyCarrot/folium-geocoder-own-locations: a geocoder that accepts a list of suggestions at creation time.
- https://github.com/iwpnd/folium-vectortilelayer: a tile layer that zooms and stretches beyond the maximum and minimum of the tile provider
